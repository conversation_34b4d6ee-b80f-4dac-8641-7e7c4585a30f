{"name": "@jupyter-notebook/tree-extension", "version": "7.4.3", "description": "Jupyter Notebook - Tree Extension", "homepage": "https://github.com/jupyter/notebook", "bugs": {"url": "https://github.com/jupyter/notebook/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyter/notebook.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/**/*.css", "style/index.js"], "scripts": {"build": "tsc -b", "build:prod": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "docs": "typedoc src", "watch": "tsc -b --watch"}, "dependencies": {"@jupyter-notebook/application": "^7.4.3", "@jupyter-notebook/tree": "^7.4.3", "@jupyterlab/application": "~4.4.3", "@jupyterlab/apputils": "~4.5.3", "@jupyterlab/coreutils": "~6.4.3", "@jupyterlab/docmanager": "~4.4.3", "@jupyterlab/filebrowser": "~4.4.3", "@jupyterlab/mainmenu": "~4.4.3", "@jupyterlab/services": "~7.4.3", "@jupyterlab/settingeditor": "~4.4.3", "@jupyterlab/settingregistry": "~4.4.3", "@jupyterlab/statedb": "~4.4.3", "@jupyterlab/translation": "~4.4.3", "@jupyterlab/ui-components": "~4.4.3", "@lumino/algorithm": "^2.0.3", "@lumino/commands": "^2.3.2", "@lumino/widgets": "^2.7.1"}, "devDependencies": {"rimraf": "^3.0.2", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}