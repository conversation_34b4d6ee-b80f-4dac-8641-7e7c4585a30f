import _plotly_utils.basevalidators


class VhoverformatValidator(_plotly_utils.basevalidators.StringValidator):
    def __init__(self, plotly_name="vhoverformat", parent_name="cone", **kwargs):
        super(VhoverformatValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            **kwargs,
        )
