import _plotly_utils.basevalidators


class MeasuresrcValidator(_plotly_utils.basevalidators.SrcValidator):
    def __init__(self, plotly_name="measuresrc", parent_name="waterfall", **kwargs):
        super(MeasuresrcValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            **kwargs,
        )
