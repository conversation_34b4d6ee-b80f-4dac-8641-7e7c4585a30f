import _plotly_utils.basevalidators


class OffsetgroupValidator(_plotly_utils.basevalidators.StringValidator):
    def __init__(self, plotly_name="offsetgroup", parent_name="violin", **kwargs):
        super(OffsetgroupValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            **kwargs,
        )
