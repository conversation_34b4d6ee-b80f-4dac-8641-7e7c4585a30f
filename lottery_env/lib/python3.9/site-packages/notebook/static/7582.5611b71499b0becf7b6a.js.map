{"version": 3, "file": "7582.5611b71499b0becf7b6a.js?v=5611b71499b0becf7b6a", "mappings": ";;;;;;AAAa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB,mBAAmB,mBAAO,CAAC,IAAoB;AAC/C;AACA;AACA;AACA,wFAAwF;AACxF;AACA;AACA;AACA,CAAC;AACD,wBAAwB;AACxB;;;;;;;ACba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB,mBAAmB,mBAAO,CAAC,IAAoB;AAC/C,wBAAwB,mBAAO,CAAC,KAAyB;AACzD;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,wFAAwF;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,wBAAwB;AACxB;;;;;;;ACxDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,GAAG,aAAa,GAAG,wBAAwB,GAAG,iBAAiB;AAC/E;AACA,8BAA8B;AAC9B,iBAAiB;AACjB,uBAAuB,UAAU,SAAS,QAAQ;AAClD;AACA;AACA,iBAAiB;AACjB;AACA;AACA,kCAAkC;AAClC,gCAAgC,UAAU;AAC1C,8BAA8B,QAAQ;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,CAAC;AACD,wBAAwB;AACxB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;;;;;;;ACvHa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,WAAW;AACX,oBAAoB,mBAAO,CAAC,KAAqB;AACjD,mBAAmB,mBAAO,CAAC,IAAoB;AAC/C,mBAAmB,mBAAO,CAAC,KAAkB;AAC7C,sCAAsC,mBAAO,CAAC,IAAqB;AACnE,oCAAoC,mBAAO,CAAC,KAAmB;AAC/D,qCAAqC,mBAAO,CAAC,KAAoB;AACjE,oCAAoC,mBAAO,CAAC,KAAmB;AAC/D,wCAAwC,mBAAO,CAAC,KAAuB;AACvE,gBAAgB,mBAAO,CAAC,KAAe;AACvC,yBAAyB,mBAAO,CAAC,KAAwB;AACzD,mBAAO,CAAC,KAAiC;AACzC;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,kCAAkC;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,6CAA6C,2DAA2D,GAAG,MAAM,EAAE,oFAAoF,gCAAgC;AAC7Q;AACA,CAAC;AACD,WAAW;AACX;;;;;;;ACzJa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,mBAAO,CAAC,KAA+B;AAC1D,oCAAoC,mBAAO,CAAC,KAAe;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,UAAU;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,UAAU;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA,SAAS,IAAI;AACb;AACA;AACA;AACA;AACA;AACA,4EAA4E,UAAU;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2GAA2G,UAAU;AACrH;AACA;AACA;AACA;AACA,wCAAwC,QAAQ;AAChD;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,gBAAgB;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qFAAqF,UAAU;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,UAAU;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA,CAAC,gCAAgC;AACjC,kBAAe;AACf;;;;;;;AC5Qa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,oBAAoB,mBAAO,CAAC,IAAwB;AACpD,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,oBAAoB,mBAAO,CAAC,KAAwB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,iDAAiD;AAC3G,2DAA2D,gDAAgD;AAC3G;AACA;AACA;AACA;AACA,wCAAwC,IAAI,MAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,GAAG,KAAK;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iGAAiG;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,yDAAyD;AACnG,wDAAwD,wBAAwB;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,OAAO;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/FindMath.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/InputJax.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MathItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/FilterUtil.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/FindTeX.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractFindMath = void 0;\nvar Options_js_1 = require(\"../util/Options.js\");\nvar AbstractFindMath = (function () {\n    function AbstractFindMath(options) {\n        var CLASS = this.constructor;\n        this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), options);\n    }\n    AbstractFindMath.OPTIONS = {};\n    return AbstractFindMath;\n}());\nexports.AbstractFindMath = AbstractFindMath;\n//# sourceMappingURL=FindMath.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractInputJax = void 0;\nvar Options_js_1 = require(\"../util/Options.js\");\nvar FunctionList_js_1 = require(\"../util/FunctionList.js\");\nvar AbstractInputJax = (function () {\n    function AbstractInputJax(options) {\n        if (options === void 0) { options = {}; }\n        this.adaptor = null;\n        this.mmlFactory = null;\n        var CLASS = this.constructor;\n        this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), options);\n        this.preFilters = new FunctionList_js_1.FunctionList();\n        this.postFilters = new FunctionList_js_1.FunctionList();\n    }\n    Object.defineProperty(AbstractInputJax.prototype, \"name\", {\n        get: function () {\n            return this.constructor.NAME;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractInputJax.prototype.setAdaptor = function (adaptor) {\n        this.adaptor = adaptor;\n    };\n    AbstractInputJax.prototype.setMmlFactory = function (mmlFactory) {\n        this.mmlFactory = mmlFactory;\n    };\n    AbstractInputJax.prototype.initialize = function () {\n    };\n    AbstractInputJax.prototype.reset = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    Object.defineProperty(AbstractInputJax.prototype, \"processStrings\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractInputJax.prototype.findMath = function (_node, _options) {\n        return [];\n    };\n    AbstractInputJax.prototype.executeFilters = function (filters, math, document, data) {\n        var args = { math: math, document: document, data: data };\n        filters.execute(args);\n        return args.data;\n    };\n    AbstractInputJax.NAME = 'generic';\n    AbstractInputJax.OPTIONS = {};\n    return AbstractInputJax;\n}());\nexports.AbstractInputJax = AbstractInputJax;\n//# sourceMappingURL=InputJax.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.newState = exports.STATE = exports.AbstractMathItem = exports.protoItem = void 0;\nfunction protoItem(open, math, close, n, start, end, display) {\n    if (display === void 0) { display = null; }\n    var item = { open: open, math: math, close: close,\n        n: n, start: { n: start }, end: { n: end }, display: display };\n    return item;\n}\nexports.protoItem = protoItem;\nvar AbstractMathItem = (function () {\n    function AbstractMathItem(math, jax, display, start, end) {\n        if (display === void 0) { display = true; }\n        if (start === void 0) { start = { i: 0, n: 0, delim: '' }; }\n        if (end === void 0) { end = { i: 0, n: 0, delim: '' }; }\n        this.root = null;\n        this.typesetRoot = null;\n        this.metrics = {};\n        this.inputData = {};\n        this.outputData = {};\n        this._state = exports.STATE.UNPROCESSED;\n        this.math = math;\n        this.inputJax = jax;\n        this.display = display;\n        this.start = start;\n        this.end = end;\n        this.root = null;\n        this.typesetRoot = null;\n        this.metrics = {};\n        this.inputData = {};\n        this.outputData = {};\n    }\n    Object.defineProperty(AbstractMathItem.prototype, \"isEscaped\", {\n        get: function () {\n            return this.display === null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMathItem.prototype.render = function (document) {\n        document.renderActions.renderMath(this, document);\n    };\n    AbstractMathItem.prototype.rerender = function (document, start) {\n        if (start === void 0) { start = exports.STATE.RERENDER; }\n        if (this.state() >= start) {\n            this.state(start - 1);\n        }\n        document.renderActions.renderMath(this, document, start);\n    };\n    AbstractMathItem.prototype.convert = function (document, end) {\n        if (end === void 0) { end = exports.STATE.LAST; }\n        document.renderActions.renderConvert(this, document, end);\n    };\n    AbstractMathItem.prototype.compile = function (document) {\n        if (this.state() < exports.STATE.COMPILED) {\n            this.root = this.inputJax.compile(this, document);\n            this.state(exports.STATE.COMPILED);\n        }\n    };\n    AbstractMathItem.prototype.typeset = function (document) {\n        if (this.state() < exports.STATE.TYPESET) {\n            this.typesetRoot = document.outputJax[this.isEscaped ? 'escaped' : 'typeset'](this, document);\n            this.state(exports.STATE.TYPESET);\n        }\n    };\n    AbstractMathItem.prototype.updateDocument = function (_document) { };\n    AbstractMathItem.prototype.removeFromDocument = function (_restore) {\n        if (_restore === void 0) { _restore = false; }\n    };\n    AbstractMathItem.prototype.setMetrics = function (em, ex, cwidth, lwidth, scale) {\n        this.metrics = {\n            em: em, ex: ex,\n            containerWidth: cwidth,\n            lineWidth: lwidth,\n            scale: scale\n        };\n    };\n    AbstractMathItem.prototype.state = function (state, restore) {\n        if (state === void 0) { state = null; }\n        if (restore === void 0) { restore = false; }\n        if (state != null) {\n            if (state < exports.STATE.INSERTED && this._state >= exports.STATE.INSERTED) {\n                this.removeFromDocument(restore);\n            }\n            if (state < exports.STATE.TYPESET && this._state >= exports.STATE.TYPESET) {\n                this.outputData = {};\n            }\n            if (state < exports.STATE.COMPILED && this._state >= exports.STATE.COMPILED) {\n                this.inputData = {};\n            }\n            this._state = state;\n        }\n        return this._state;\n    };\n    AbstractMathItem.prototype.reset = function (restore) {\n        if (restore === void 0) { restore = false; }\n        this.state(exports.STATE.UNPROCESSED, restore);\n    };\n    return AbstractMathItem;\n}());\nexports.AbstractMathItem = AbstractMathItem;\nexports.STATE = {\n    UNPROCESSED: 0,\n    FINDMATH: 10,\n    COMPILED: 20,\n    CONVERT: 100,\n    METRICS: 110,\n    RERENDER: 125,\n    TYPESET: 150,\n    INSERTED: 200,\n    LAST: 10000\n};\nfunction newState(name, state) {\n    if (name in exports.STATE) {\n        throw Error('State ' + name + ' already exists');\n    }\n    exports.STATE[name] = state;\n}\nexports.newState = newState;\n//# sourceMappingURL=MathItem.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TeX = void 0;\nvar InputJax_js_1 = require(\"../core/InputJax.js\");\nvar Options_js_1 = require(\"../util/Options.js\");\nvar FindTeX_js_1 = require(\"./tex/FindTeX.js\");\nvar FilterUtil_js_1 = __importDefault(require(\"./tex/FilterUtil.js\"));\nvar NodeUtil_js_1 = __importDefault(require(\"./tex/NodeUtil.js\"));\nvar TexParser_js_1 = __importDefault(require(\"./tex/TexParser.js\"));\nvar TexError_js_1 = __importDefault(require(\"./tex/TexError.js\"));\nvar ParseOptions_js_1 = __importDefault(require(\"./tex/ParseOptions.js\"));\nvar Tags_js_1 = require(\"./tex/Tags.js\");\nvar Configuration_js_1 = require(\"./tex/Configuration.js\");\nrequire(\"./tex/base/BaseConfiguration.js\");\nvar TeX = (function (_super) {\n    __extends(TeX, _super);\n    function TeX(options) {\n        if (options === void 0) { options = {}; }\n        var _this = this;\n        var _a = __read((0, Options_js_1.separateOptions)(options, TeX.OPTIONS, FindTeX_js_1.FindTeX.OPTIONS), 3), rest = _a[0], tex = _a[1], find = _a[2];\n        _this = _super.call(this, tex) || this;\n        _this.findTeX = _this.options['FindTeX'] || new FindTeX_js_1.FindTeX(find);\n        var packages = _this.options.packages;\n        var configuration = _this.configuration = TeX.configure(packages);\n        var parseOptions = _this._parseOptions =\n            new ParseOptions_js_1.default(configuration, [_this.options, Tags_js_1.TagsFactory.OPTIONS]);\n        (0, Options_js_1.userOptions)(parseOptions.options, rest);\n        configuration.config(_this);\n        TeX.tags(parseOptions, configuration);\n        _this.postFilters.add(FilterUtil_js_1.default.cleanSubSup, -6);\n        _this.postFilters.add(FilterUtil_js_1.default.setInherited, -5);\n        _this.postFilters.add(FilterUtil_js_1.default.moveLimits, -4);\n        _this.postFilters.add(FilterUtil_js_1.default.cleanStretchy, -3);\n        _this.postFilters.add(FilterUtil_js_1.default.cleanAttributes, -2);\n        _this.postFilters.add(FilterUtil_js_1.default.combineRelations, -1);\n        return _this;\n    }\n    TeX.configure = function (packages) {\n        var configuration = new Configuration_js_1.ParserConfiguration(packages, ['tex']);\n        configuration.init();\n        return configuration;\n    };\n    TeX.tags = function (options, configuration) {\n        Tags_js_1.TagsFactory.addTags(configuration.tags);\n        Tags_js_1.TagsFactory.setDefault(options.options.tags);\n        options.tags = Tags_js_1.TagsFactory.getDefault();\n        options.tags.configuration = options;\n    };\n    TeX.prototype.setMmlFactory = function (mmlFactory) {\n        _super.prototype.setMmlFactory.call(this, mmlFactory);\n        this._parseOptions.nodeFactory.setMmlFactory(mmlFactory);\n    };\n    Object.defineProperty(TeX.prototype, \"parseOptions\", {\n        get: function () {\n            return this._parseOptions;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TeX.prototype.reset = function (tag) {\n        if (tag === void 0) { tag = 0; }\n        this.parseOptions.tags.reset(tag);\n    };\n    TeX.prototype.compile = function (math, document) {\n        this.parseOptions.clear();\n        this.executeFilters(this.preFilters, math, document, this.parseOptions);\n        var display = math.display;\n        this.latex = math.math;\n        var node;\n        this.parseOptions.tags.startEquation(math);\n        var globalEnv;\n        try {\n            var parser = new TexParser_js_1.default(this.latex, { display: display, isInner: false }, this.parseOptions);\n            node = parser.mml();\n            globalEnv = parser.stack.global;\n        }\n        catch (err) {\n            if (!(err instanceof TexError_js_1.default)) {\n                throw err;\n            }\n            this.parseOptions.error = true;\n            node = this.options.formatError(this, err);\n        }\n        node = this.parseOptions.nodeFactory.create('node', 'math', [node]);\n        if (globalEnv === null || globalEnv === void 0 ? void 0 : globalEnv.indentalign) {\n            NodeUtil_js_1.default.setAttribute(node, 'indentalign', globalEnv.indentalign);\n        }\n        if (display) {\n            NodeUtil_js_1.default.setAttribute(node, 'display', 'block');\n        }\n        this.parseOptions.tags.finishEquation(math);\n        this.parseOptions.root = node;\n        this.executeFilters(this.postFilters, math, document, this.parseOptions);\n        this.mathNode = this.parseOptions.root;\n        return this.mathNode;\n    };\n    TeX.prototype.findMath = function (strings) {\n        return this.findTeX.findMath(strings);\n    };\n    TeX.prototype.formatError = function (err) {\n        var message = err.message.replace(/\\n.*/, '');\n        return this.parseOptions.nodeFactory.create('error', message, err.id, this.latex);\n    };\n    TeX.NAME = 'TeX';\n    TeX.OPTIONS = __assign(__assign({}, InputJax_js_1.AbstractInputJax.OPTIONS), { FindTeX: null, packages: ['base'], digits: /^(?:[0-9]+(?:\\{,\\}[0-9]{3})*(?:\\.[0-9]*)?|\\.[0-9]+)/, maxBuffer: 5 * 1024, formatError: function (jax, err) { return jax.formatError(err); } });\n    return TeX;\n}(InputJax_js_1.AbstractInputJax));\nexports.TeX = TeX;\n//# sourceMappingURL=tex.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar MmlNode_js_1 = require(\"../../core/MmlTree/MmlNode.js\");\nvar NodeUtil_js_1 = __importDefault(require(\"./NodeUtil.js\"));\nvar FilterUtil;\n(function (FilterUtil) {\n    FilterUtil.cleanStretchy = function (arg) {\n        var e_1, _a;\n        var options = arg.data;\n        try {\n            for (var _b = __values(options.getList('fixStretchy')), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var mo = _c.value;\n                if (NodeUtil_js_1.default.getProperty(mo, 'fixStretchy')) {\n                    var symbol = NodeUtil_js_1.default.getForm(mo);\n                    if (symbol && symbol[3] && symbol[3]['stretchy']) {\n                        NodeUtil_js_1.default.setAttribute(mo, 'stretchy', false);\n                    }\n                    var parent_1 = mo.parent;\n                    if (!NodeUtil_js_1.default.getTexClass(mo) && (!symbol || !symbol[2])) {\n                        var texAtom = options.nodeFactory.create('node', 'TeXAtom', [mo]);\n                        parent_1.replaceChild(texAtom, mo);\n                        texAtom.inheritAttributesFrom(mo);\n                    }\n                    NodeUtil_js_1.default.removeProperties(mo, 'fixStretchy');\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    FilterUtil.cleanAttributes = function (arg) {\n        var node = arg.data.root;\n        node.walkTree(function (mml, _d) {\n            var e_2, _a;\n            var attribs = mml.attributes;\n            if (!attribs) {\n                return;\n            }\n            var keep = new Set((attribs.get('mjx-keep-attrs') || '').split(/ /));\n            delete (attribs.getAllAttributes())['mjx-keep-attrs'];\n            try {\n                for (var _b = __values(attribs.getExplicitNames()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var key = _c.value;\n                    if (!keep.has(key) && attribs.attributes[key] === mml.attributes.getInherited(key)) {\n                        delete attribs.attributes[key];\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }, {});\n    };\n    FilterUtil.combineRelations = function (arg) {\n        var e_3, _a, e_4, _b;\n        var remove = [];\n        try {\n            for (var _c = __values(arg.data.getList('mo')), _e = _c.next(); !_e.done; _e = _c.next()) {\n                var mo = _e.value;\n                if (mo.getProperty('relationsCombined') || !mo.parent ||\n                    (mo.parent && !NodeUtil_js_1.default.isType(mo.parent, 'mrow')) ||\n                    NodeUtil_js_1.default.getTexClass(mo) !== MmlNode_js_1.TEXCLASS.REL) {\n                    continue;\n                }\n                var mml = mo.parent;\n                var m2 = void 0;\n                var children = mml.childNodes;\n                var next = children.indexOf(mo) + 1;\n                var variantForm = NodeUtil_js_1.default.getProperty(mo, 'variantForm');\n                while (next < children.length && (m2 = children[next]) &&\n                    NodeUtil_js_1.default.isType(m2, 'mo') &&\n                    NodeUtil_js_1.default.getTexClass(m2) === MmlNode_js_1.TEXCLASS.REL) {\n                    if (variantForm === NodeUtil_js_1.default.getProperty(m2, 'variantForm') &&\n                        _compareExplicit(mo, m2)) {\n                        NodeUtil_js_1.default.appendChildren(mo, NodeUtil_js_1.default.getChildren(m2));\n                        _copyExplicit(['stretchy', 'rspace'], mo, m2);\n                        try {\n                            for (var _f = (e_4 = void 0, __values(m2.getPropertyNames())), _g = _f.next(); !_g.done; _g = _f.next()) {\n                                var name_1 = _g.value;\n                                mo.setProperty(name_1, m2.getProperty(name_1));\n                            }\n                        }\n                        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                        finally {\n                            try {\n                                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                            }\n                            finally { if (e_4) throw e_4.error; }\n                        }\n                        children.splice(next, 1);\n                        remove.push(m2);\n                        m2.parent = null;\n                        m2.setProperty('relationsCombined', true);\n                    }\n                    else {\n                        if (mo.attributes.getExplicit('rspace') == null) {\n                            NodeUtil_js_1.default.setAttribute(mo, 'rspace', '0pt');\n                        }\n                        if (m2.attributes.getExplicit('lspace') == null) {\n                            NodeUtil_js_1.default.setAttribute(m2, 'lspace', '0pt');\n                        }\n                        break;\n                    }\n                }\n                mo.attributes.setInherited('form', mo.getForms()[0]);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_e && !_e.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        arg.data.removeFromList('mo', remove);\n    };\n    var _copyExplicit = function (attrs, node1, node2) {\n        var attr1 = node1.attributes;\n        var attr2 = node2.attributes;\n        attrs.forEach(function (x) {\n            var attr = attr2.getExplicit(x);\n            if (attr != null) {\n                attr1.set(x, attr);\n            }\n        });\n    };\n    var _compareExplicit = function (node1, node2) {\n        var e_5, _a;\n        var filter = function (attr, space) {\n            var exp = attr.getExplicitNames();\n            return exp.filter(function (x) {\n                return x !== space &&\n                    (x !== 'stretchy' ||\n                        attr.getExplicit('stretchy'));\n            });\n        };\n        var attr1 = node1.attributes;\n        var attr2 = node2.attributes;\n        var exp1 = filter(attr1, 'lspace');\n        var exp2 = filter(attr2, 'rspace');\n        if (exp1.length !== exp2.length) {\n            return false;\n        }\n        try {\n            for (var exp1_1 = __values(exp1), exp1_1_1 = exp1_1.next(); !exp1_1_1.done; exp1_1_1 = exp1_1.next()) {\n                var name_2 = exp1_1_1.value;\n                if (attr1.getExplicit(name_2) !== attr2.getExplicit(name_2)) {\n                    return false;\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (exp1_1_1 && !exp1_1_1.done && (_a = exp1_1.return)) _a.call(exp1_1);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        return true;\n    };\n    var _cleanSubSup = function (options, low, up) {\n        var e_6, _a;\n        var remove = [];\n        try {\n            for (var _b = __values(options.getList('m' + low + up)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var mml = _c.value;\n                var children = mml.childNodes;\n                if (children[mml[low]] && children[mml[up]]) {\n                    continue;\n                }\n                var parent_2 = mml.parent;\n                var newNode = (children[mml[low]] ?\n                    options.nodeFactory.create('node', 'm' + low, [children[mml.base], children[mml[low]]]) :\n                    options.nodeFactory.create('node', 'm' + up, [children[mml.base], children[mml[up]]]));\n                NodeUtil_js_1.default.copyAttributes(mml, newNode);\n                if (parent_2) {\n                    parent_2.replaceChild(newNode, mml);\n                }\n                else {\n                    options.root = newNode;\n                }\n                remove.push(mml);\n            }\n        }\n        catch (e_6_1) { e_6 = { error: e_6_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_6) throw e_6.error; }\n        }\n        options.removeFromList('m' + low + up, remove);\n    };\n    FilterUtil.cleanSubSup = function (arg) {\n        var options = arg.data;\n        if (options.error) {\n            return;\n        }\n        _cleanSubSup(options, 'sub', 'sup');\n        _cleanSubSup(options, 'under', 'over');\n    };\n    var _moveLimits = function (options, underover, subsup) {\n        var e_7, _a;\n        var remove = [];\n        try {\n            for (var _b = __values(options.getList(underover)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var mml = _c.value;\n                if (mml.attributes.get('displaystyle')) {\n                    continue;\n                }\n                var base = mml.childNodes[mml.base];\n                var mo = base.coreMO();\n                if (base.getProperty('movablelimits') && !mo.attributes.getExplicit('movablelimits')) {\n                    var node = options.nodeFactory.create('node', subsup, mml.childNodes);\n                    NodeUtil_js_1.default.copyAttributes(mml, node);\n                    if (mml.parent) {\n                        mml.parent.replaceChild(node, mml);\n                    }\n                    else {\n                        options.root = node;\n                    }\n                    remove.push(mml);\n                }\n            }\n        }\n        catch (e_7_1) { e_7 = { error: e_7_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_7) throw e_7.error; }\n        }\n        options.removeFromList(underover, remove);\n    };\n    FilterUtil.moveLimits = function (arg) {\n        var options = arg.data;\n        _moveLimits(options, 'munderover', 'msubsup');\n        _moveLimits(options, 'munder', 'msub');\n        _moveLimits(options, 'mover', 'msup');\n    };\n    FilterUtil.setInherited = function (arg) {\n        arg.data.root.setInheritedAttributes({}, arg.math['display'], 0, false);\n    };\n})(FilterUtil || (FilterUtil = {}));\nexports.default = FilterUtil;\n//# sourceMappingURL=FilterUtil.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FindTeX = void 0;\nvar FindMath_js_1 = require(\"../../core/FindMath.js\");\nvar string_js_1 = require(\"../../util/string.js\");\nvar MathItem_js_1 = require(\"../../core/MathItem.js\");\nvar FindTeX = (function (_super) {\n    __extends(FindTeX, _super);\n    function FindTeX(options) {\n        var _this = _super.call(this, options) || this;\n        _this.getPatterns();\n        return _this;\n    }\n    FindTeX.prototype.getPatterns = function () {\n        var _this = this;\n        var options = this.options;\n        var starts = [], parts = [], subparts = [];\n        this.end = {};\n        this.env = this.sub = 0;\n        var i = 1;\n        options['inlineMath'].forEach(function (delims) { return _this.addPattern(starts, delims, false); });\n        options['displayMath'].forEach(function (delims) { return _this.addPattern(starts, delims, true); });\n        if (starts.length) {\n            parts.push(starts.sort(string_js_1.sortLength).join('|'));\n        }\n        if (options['processEnvironments']) {\n            parts.push('\\\\\\\\begin\\\\s*\\\\{([^}]*)\\\\}');\n            this.env = i;\n            i++;\n        }\n        if (options['processEscapes']) {\n            subparts.push('\\\\\\\\([\\\\\\\\$])');\n        }\n        if (options['processRefs']) {\n            subparts.push('(\\\\\\\\(?:eq)?ref\\\\s*\\\\{[^}]*\\\\})');\n        }\n        if (subparts.length) {\n            parts.push('(' + subparts.join('|') + ')');\n            this.sub = i;\n        }\n        this.start = new RegExp(parts.join('|'), 'g');\n        this.hasPatterns = (parts.length > 0);\n    };\n    FindTeX.prototype.addPattern = function (starts, delims, display) {\n        var _a = __read(delims, 2), open = _a[0], close = _a[1];\n        starts.push((0, string_js_1.quotePattern)(open));\n        this.end[open] = [close, display, this.endPattern(close)];\n    };\n    FindTeX.prototype.endPattern = function (end, endp) {\n        return new RegExp((endp || (0, string_js_1.quotePattern)(end)) + '|\\\\\\\\(?:[a-zA-Z]|.)|[{}]', 'g');\n    };\n    FindTeX.prototype.findEnd = function (text, n, start, end) {\n        var _a = __read(end, 3), close = _a[0], display = _a[1], pattern = _a[2];\n        var i = pattern.lastIndex = start.index + start[0].length;\n        var match, braces = 0;\n        while ((match = pattern.exec(text))) {\n            if ((match[1] || match[0]) === close && braces === 0) {\n                return (0, MathItem_js_1.protoItem)(start[0], text.substr(i, match.index - i), match[0], n, start.index, match.index + match[0].length, display);\n            }\n            else if (match[0] === '{') {\n                braces++;\n            }\n            else if (match[0] === '}' && braces) {\n                braces--;\n            }\n        }\n        return null;\n    };\n    FindTeX.prototype.findMathInString = function (math, n, text) {\n        var start, match;\n        this.start.lastIndex = 0;\n        while ((start = this.start.exec(text))) {\n            if (start[this.env] !== undefined && this.env) {\n                var end = '\\\\\\\\end\\\\s*(\\\\{' + (0, string_js_1.quotePattern)(start[this.env]) + '\\\\})';\n                match = this.findEnd(text, n, start, ['{' + start[this.env] + '}', true, this.endPattern(null, end)]);\n                if (match) {\n                    match.math = match.open + match.math + match.close;\n                    match.open = match.close = '';\n                }\n            }\n            else if (start[this.sub] !== undefined && this.sub) {\n                var math_1 = start[this.sub];\n                var end = start.index + start[this.sub].length;\n                if (math_1.length === 2) {\n                    match = (0, MathItem_js_1.protoItem)('', math_1.substr(1), '', n, start.index, end);\n                }\n                else {\n                    match = (0, MathItem_js_1.protoItem)('', math_1, '', n, start.index, end, false);\n                }\n            }\n            else {\n                match = this.findEnd(text, n, start, this.end[start[0]]);\n            }\n            if (match) {\n                math.push(match);\n                this.start.lastIndex = match.end.n;\n            }\n        }\n    };\n    FindTeX.prototype.findMath = function (strings) {\n        var math = [];\n        if (this.hasPatterns) {\n            for (var i = 0, m = strings.length; i < m; i++) {\n                this.findMathInString(math, i, strings[i]);\n            }\n        }\n        return math;\n    };\n    FindTeX.OPTIONS = {\n        inlineMath: [\n            ['\\\\(', '\\\\)']\n        ],\n        displayMath: [\n            ['$$', '$$'],\n            ['\\\\[', '\\\\]']\n        ],\n        processEscapes: true,\n        processEnvironments: true,\n        processRefs: true,\n    };\n    return FindTeX;\n}(FindMath_js_1.AbstractFindMath));\nexports.FindTeX = FindTeX;\n//# sourceMappingURL=FindTeX.js.map"], "names": [], "sourceRoot": ""}