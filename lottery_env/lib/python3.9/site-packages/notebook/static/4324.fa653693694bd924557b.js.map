{"version": 3, "file": "4324.fa653693694bd924557b.js?v=fa653693694bd924557b", "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oCAAoC;;AAEpC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB,qBAAM,gBAAgB,qBAAM,IAAI,qBAAM,sBAAsB,qBAAM;;AAE1F;AACA;;AAEA;AACA;;AAEA;AACA,kBAAkB,KAA0B;;AAE5C;AACA,gCAAgC,QAAa;;AAE7C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ,CAAC;;AAED;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,GAAG;AACd,WAAW,OAAO;AAClB,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,QAAQ;AACnB;AACA,IAAI;AACJ,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,SAAS;AACpB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,aAAa,UAAU;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,aAAa;AACxB,aAAa,aAAa;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,OAAO;AAClB,WAAW,QAAQ,UAAU;AAC7B,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA,wBAAwB;;AAExB;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,QAAQ;AACnB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,GAAG;AAChB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,UAAU;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA,kBAAkB;AAClB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA,8BAA8B,mBAAmB;AACjD;AACA;AACA;AACA;AACA;AACA,+CAA+C,mBAAmB;AAClE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,gBAAgB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB,WAAW;AACX;AACA,cAAc,QAAQ;AACtB,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,WAAW;AACtB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB,iBAAiB;AACjB;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,UAAU;AACvB;AACA;AACA,yCAAyC,QAAQ;AACjD;AACA;AACA,YAAY,QAAQ,IAAI,QAAQ;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,GAAG;AAChB;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;ACx7Da;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,oCAAoC;AACnD;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B,GAAG,4BAA4B,GAAG,4BAA4B;AAC7F,cAAc,mBAAO,CAAC,KAAO;AAC7B;AACA;AACA,cAAc,mBAAO,CAAC,KAAe;AACrC,aAAa,mBAAO,CAAC,KAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,+BAA+B;;;;;;;;;AC3ElB;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc,mBAAO,CAAC,KAAe;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sHAAsH,SAAS;AAC/H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,0HAA0H,gBAAgB;AAC1I;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qBAAqB;AACrB,SAAS;AACT;AACA;AACA,qBAAqB;AACrB,SAAS;AACT;AACA;AACA,qBAAqB;AACrB,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,kBAAkB;AACnB,kBAAe;;;;;;;;;AC3JF;AACb;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB,GAAG,qBAAqB,GAAG,+BAA+B,GAAG,kBAAkB,GAAG,yBAAyB,GAAG,0BAA0B,GAAG,6BAA6B,GAAG,oCAAoC,GAAG,6BAA6B,GAAG,qBAAqB,GAAG,mCAAmC,GAAG,6BAA6B,GAAG,qBAAqB,GAAG,mCAAmC,GAAG,iCAAiC,GAAG,yBAAyB,GAAG,+BAA+B,GAAG,eAAe,GAAG,aAAa,GAAG,kBAAkB,GAAG,gBAAgB,GAAG,aAAa,GAAG,iBAAiB,GAAG,2BAA2B,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,wBAAwB,GAAG,kBAAkB,GAAG,qBAAqB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,mBAAmB,GAAG,eAAe,GAAG,WAAW;AACzwC,uBAAuB,GAAG,4BAA4B,GAAG,kCAAkC,GAAG,oCAAoC,GAAG,uBAAuB,GAAG,wBAAwB,GAAG,4BAA4B,GAAG,4BAA4B,GAAG,mBAAmB,GAAG,mBAAmB,GAAG,aAAa;AACjT,mBAAmB,mBAAO,CAAC,KAAY;AACvC,2CAA0C,EAAE,qCAAqC,8BAA8B,EAAC;AAChH,+CAA8C,EAAE,qCAAqC,kCAAkC,EAAC;AACxH,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,gDAA+C,EAAE,qCAAqC,mCAAmC,EAAC;AAC1H,iDAAgD,EAAE,qCAAqC,oCAAoC,EAAC;AAC5H,8CAA6C,EAAE,qCAAqC,iCAAiC,EAAC;AACtH,oDAAmD,EAAE,qCAAqC,uCAAuC,EAAC;AAClI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,qDAAoD,EAAE,qCAAqC,wCAAwC,EAAC;AACpI,uDAAsD,EAAE,qCAAqC,0CAA0C,EAAC;AACxI,oBAAoB,mBAAO,CAAC,KAAa;AACzC,6CAA4C,EAAE,qCAAqC,iCAAiC,EAAC;AACrH,4CAA2C,EAAE,qCAAqC,gCAAgC,EAAC;AACnH,yCAAwC,EAAE,qCAAqC,6BAA6B,EAAC;AAC7G,qBAAqB,mBAAO,CAAC,KAAc;AAC3C,8CAA6C,EAAE,qCAAqC,mCAAmC,EAAC;AACxH,iBAAiB,mBAAO,CAAC,GAAU;AACnC,yCAAwC,EAAE,qCAAqC,0BAA0B,EAAC;AAC1G,2CAA0C,EAAE,qCAAqC,4BAA4B,EAAC;AAC9G,uBAAuB,mBAAO,CAAC,KAAgB;AAC/C,2DAA0D,EAAE,qCAAqC,kDAAkD,EAAC;AACpJ,qDAAoD,EAAE,qCAAqC,4CAA4C,EAAC;AACxI,kCAAkC,mBAAO,CAAC,KAA2B;AACrE,6DAA4D,EAAE,qCAAqC,+DAA+D,EAAC;AACnK,+DAA8D,EAAE,qCAAqC,iEAAiE,EAAC;AACvK,wBAAwB,mBAAO,CAAC,KAAiB;AACjD,iDAAgD,EAAE,qCAAqC,yCAAyC,EAAC;AACjI,yDAAwD,EAAE,qCAAqC,iDAAiD,EAAC;AACjJ,+DAA8D,EAAE,qCAAqC,uDAAuD,EAAC;AAC7J,wBAAwB,mBAAO,CAAC,KAAiB;AACjD,iDAAgD,EAAE,qCAAqC,yCAAyC,EAAC;AACjI,yDAAwD,EAAE,qCAAqC,iDAAiD,EAAC;AACjJ,gEAA+D,EAAE,qCAAqC,wDAAwD,EAAC;AAC/J,wBAAwB,mBAAO,CAAC,KAAiB;AACjD,yDAAwD,EAAE,qCAAqC,iDAAiD,EAAC;AACjJ,qBAAqB,mBAAO,CAAC,IAAc;AAC3C,sDAAqD,EAAE,qCAAqC,2CAA2C,EAAC;AACxI,qDAAoD,EAAE,qCAAqC,0CAA0C,EAAC;AACtI,8CAA6C,EAAE,qCAAqC,mCAAmC,EAAC;AACxH,2DAA0D,EAAE,qCAAqC,gDAAgD,EAAC;AAClJ,iDAAgD,EAAE,qCAAqC,sCAAsC,EAAC;AAC9H,gDAA+C,EAAE,qCAAqC,qCAAqC,EAAC;AAC5H,yCAAwC,EAAE,qCAAqC,8BAA8B,EAAC;AAC9G,+CAA8C,EAAE,qCAAqC,oCAAoC,EAAC;AAC1H,+CAA8C,EAAE,qCAAqC,oCAAoC,EAAC;AAC1H,wDAAuD,EAAE,qCAAqC,6CAA6C,EAAC;AAC5I,wDAAuD,EAAE,qCAAqC,6CAA6C,EAAC;AAC5I,oDAAmD,EAAE,qCAAqC,yCAAyC,EAAC;AACpI,mDAAkD,EAAE,qCAAqC,wCAAwC,EAAC;AAClI,gEAA+D,EAAE,qCAAqC,qDAAqD,EAAC;AAC5J,8DAA6D,EAAE,qCAAqC,mDAAmD,EAAC;AACxJ,wDAAuD,EAAE,qCAAqC,6CAA6C,EAAC;AAC5I,mDAAkD,EAAE,qCAAqC,wCAAwC,EAAC;AAClI,cAAc,mBAAO,CAAC,KAAO;AAC7B,WAAW;;;;;;;;;AChFE;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B,GAAG,yBAAyB;AAC3D,cAAc,mBAAO,CAAC,KAAO;AAC7B,WAAW,mBAAO,CAAC,KAAM;AACzB,iBAAiB,mBAAO,CAAC,GAAU;AACnC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,wBAAwB,yBAAyB,yBAAyB;AAC3E;AACA;AACA,aAAa,YAAY;AACzB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;;;;;;;;;AC/FlB;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,+BAA+B,GAAG,yBAAyB,GAAG,uBAAuB,GAAG,4BAA4B,GAAG,kCAAkC,GAAG,oCAAoC,GAAG,2CAA2C,GAAG,sCAAsC,GAAG,0BAA0B,GAAG,uBAAuB,GAAG,wBAAwB,GAAG,4BAA4B,GAAG,4BAA4B,GAAG,mBAAmB,GAAG,mBAAmB,GAAG,aAAa,GAAG,kBAAkB,GAAG,oBAAoB,GAAG,qBAAqB;AACviB,cAAc,mBAAO,CAAC,KAAO;AAC7B,WAAW,mBAAO,CAAC,KAAM;AACzB,mBAAmB,mBAAO,CAAC,KAAY;AACvC,oBAAoB,mBAAO,CAAC,KAAa;AACzC,iBAAiB,mBAAO,CAAC,GAAU;AACnC,uBAAuB,mBAAO,CAAC,KAAgB;AAC/C;AACA;AACA;AACA,CAAC,gDAAgD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,oBAAoB,qBAAqB,qBAAqB;AAC/D;AACA;AACA;AACA,CAAC,oDAAoD;AACrD;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,gDAAgD;AACjD,kBAAkB;AAClB,oBAAoB;AACpB,mBAAmB;AACnB,mBAAmB;AACnB;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,YAAY,aAAa,aAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,kBAAkB,mBAAmB,mBAAmB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,YAAY,aAAa,aAAa;AACvC;AACA;AACA;AACA;AACA,CAAC,kBAAkB,mBAAmB,mBAAmB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,kBAAkB,mBAAmB,mBAAmB;AACzD;AACA;AACA;AACA,CAAC,2BAA2B,4BAA4B,4BAA4B;AACpF;AACA;AACA;AACA,CAAC,2BAA2B,4BAA4B,4BAA4B;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,uBAAuB,wBAAwB,wBAAwB;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,yBAAyB,0BAA0B,0BAA0B;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,qCAAqC,sCAAsC,sCAAsC;AAClH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,0CAA0C,2CAA2C,2CAA2C;AACjI;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,CAAC,mCAAmC,oCAAoC,oCAAoC;AAC5G;AACA;AACA;AACA;AACA,oEAAoE,IAAI;AACxE,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,CAAC,iCAAiC,kCAAkC,kCAAkC;AACtG;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,CAAC,2BAA2B,4BAA4B,4BAA4B;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,sBAAsB,uBAAuB,uBAAuB;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,wBAAwB,yBAAyB,yBAAyB;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,0CAA0C;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oHAAoH,uBAAuB,UAAU,qBAAqB;AAC1K;AACA;AACA;AACA;AACA;AACA;AACA,oHAAoH,uBAAuB;AAC3I;AACA;AACA;AACA;AACA;AACA;AACA,oHAAoH,uBAAuB;AAC3I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,oHAAoH,uBAAuB,uBAAuB,cAAc;AAChL;AACA;AACA,oHAAoH,uBAAuB;AAC3I;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4GAA4G,uBAAuB,uBAAuB,cAAc;AACxK;AACA;AACA,4GAA4G,uBAAuB;AACnI;AACA;AACA;AACA;AACA,8GAA8G,sBAAsB;AACpI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kFAAkF,oDAAoD;AACtI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,uBAAuB,yBAAyB,cAAc;AACxH;AACA;AACA,0DAA0D,uBAAuB;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D,gBAAgB,UAAU,qBAAqB;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,oCAAoC;AACtF;AACA;AACA;AACA;AACA,iEAAiE,gBAAgB;AACjF;AACA;AACA,iEAAiE,gBAAgB,UAAU,qBAAqB,sBAAsB,eAAe;AACrJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,gBAAgB;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,eAAe,yBAAyB,cAAc;AAChH;AACA;AACA,0DAA0D,eAAe;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kGAAkG,iCAAiC;AACnI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,+BAA+B;AACjE;AACA,2CAA2C,gBAAgB,KAAK,WAAW;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,+BAA+B;AACrE;AACA;AACA;AACA;AACA;AACA,gDAAgD,eAAe;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,mCAAmC;AAC7E;AACA;AACA;AACA,0CAA0C,+BAA+B;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,QAAQ,KAAK,WAAW,8BAA8B,uBAAuB;AACzH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,+BAA+B;AACjE;AACA,4CAA4C,gBAAgB,KAAK,WAAW;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,+BAA+B;AACrE;AACA;AACA;AACA;AACA;AACA,iDAAiD,eAAe;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,mCAAmC;AAC7E;AACA;AACA;AACA,0CAA0C,+BAA+B;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,uBAAuB,GAAG,mBAAmB;AAC/G,iDAAiD,wBAAwB,KAAK,WAAW,QAAQ,wCAAwC,KAAK,MAAM;AACpJ;AACA;AACA,gDAAgD,YAAY;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,+BAA+B;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,yCAAyC;AACzE;AACA;AACA;AACA,gDAAgD,oBAAoB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,gBAAgB;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,0BAA0B;AAC/E;AACA;AACA;AACA,4DAA4D,eAAe;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,8DAA8D,OAAO;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,4EAA4E,cAAc;AAC1F,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,gBAAgB;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wGAAwG,GAAG;AAC3G;AACA;AACA;AACA;AACA,+EAA+E,IAAI;AACnF,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,mCAAmC;AACnF;AACA;AACA;AACA;AACA;AACA,uDAAuD,eAAe;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,+BAA+B;AAC9G;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,+BAA+B;;;;;;;;;AC3rClB;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,iBAAiB,kBAAkB,kBAAkB;;;;;;;;;ACfzC;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,aAAa;AAC/B,cAAc,mBAAO,CAAC,KAAO;AAC7B;AACA;AACA,0BAA0B;AAC1B,+BAA+B;AAC/B,CAAC,YAAY,aAAa,aAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,+CAA+C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,SAAS;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,SAAS;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;;;;;;;;;AC/Ha;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,GAAG,cAAc,GAAG,cAAc,GAAG,eAAe;AACtH;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,mBAAmB;;;;;;;;;AClCN;AACb;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,GAAG,iBAAiB,GAAG,aAAa;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,YAAY,aAAa,aAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;;;;;;;;;AC7YH;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wBAAwB;AAChD;AACA;AACA;AACA,yFAAyF,OAAO;AAChG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;;;;;;;;;ACvJhB;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mCAAmC,GAAG,6BAA6B,GAAG,qBAAqB;AAC3F,cAAc,mBAAO,CAAC,KAAO;AAC7B,WAAW,mBAAO,CAAC,KAAM;AACzB,iBAAiB,mBAAO,CAAC,GAAU;AACnC,oBAAoB,mBAAO,CAAC,KAAa;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,oBAAoB,qBAAqB,qBAAqB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,qDAAqD;AACpH;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,CAAC,oEAAoE;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oGAAoG,4CAA4C;AAChJ;AACA;AACA;AACA;AACA,+FAA+F,cAAc;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,2CAA2C;AACrF;AACA;AACA,SAAS;AACT;AACA;AACA,mCAAmC;;;;;;;;;ACpMtB;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oCAAoC,GAAG,6BAA6B,GAAG,qBAAqB;AAC5F,cAAc,mBAAO,CAAC,KAAO;AAC7B,WAAW,mBAAO,CAAC,KAAM;AACzB,oBAAoB,mBAAO,CAAC,KAAa;AACzC,iBAAiB,mBAAO,CAAC,GAAU;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,oBAAoB,qBAAqB,qBAAqB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,qDAAqD;AACpH;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,CAAC,oEAAoE;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;;;;;;;;;AClHvB;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,yBAAyB,GAAG,wBAAwB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,mBAAmB,GAAG,oBAAoB,GAAG,gCAAgC,GAAG,2BAA2B,GAAG,qBAAqB,GAAG,kBAAkB;AAC5qB,WAAW,mBAAO,CAAC,KAAM;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,iBAAiB,kBAAkB,kBAAkB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,cAAc,eAAe,eAAe;;;;;;;;;ACjThC;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,kBAAkB;AACnB,kBAAe;;;;;;;;;ACtBF;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,iBAAiB;AACjB,cAAc,mBAAO,CAAC,KAAO;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,wBAAwB;AACzD;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;;;;;;;;;ACnEJ;AACb;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mCAAmC,GAAG,iCAAiC;AACvE,uBAAuB,mBAAO,CAAC,KAAgB;AAC/C;AACA;AACA;AACA;AACA,CAAC,8CAA8C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;AC3EnC;AACA;AACA;AACA;AAC4C;AACrC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACsB;AACtB;;;;AC5BA;AACA;AACA;AACA;AACgF;AACzE,qCAAqC,mCAAqB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE,KAAK,aAAa,OAAO;AAC/F;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,OAAO;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AC5EA;AACA;AACA;AACA;AACgF;AACzE,qCAAqC,mCAAqB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACxBA;AACA;AACA;AACA;AACyD;AACP;AACA;AAC3C;AACP,8BAA8B,sBAAsB;AACpD,8BAA8B,sBAAsB;AACpD,uBAAuB,gCAAuB;AAC9C;AACA;AACA;AACA;;ACdA;AACA;AACA;AACA;AACyB;AACA;AACA;AACI;AAC7B;;ACRO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACjBA;AACA;AACA;AACA;AACqD;AACrD;AACA;AACA;AACA;AACyC;AAClC;AACP,YAAY,0BAA0B;AACtC,yCAAyC,aAAa;AACtD;AACA;AACA,2BAA2B,yBAAyB;AACpD;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;ACtCA;AACA;AACA;AACA;AAC+B;AACoB;AACtB;AACJ;AACA;AACI;AAC7B", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash.mergewith/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/browser/main.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/browser/ril.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/api.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/cancellation.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/connection.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/disposable.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/events.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/is.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/linkedMap.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/messageBuffer.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/messageReader.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/messageWriter.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/messages.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/ral.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/semaphore.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-jsonrpc/lib/common/sharedArrayCancellation.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-ws-jsonrpc/lib/disposable.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-ws-jsonrpc/lib/socket/reader.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-ws-jsonrpc/lib/socket/writer.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-ws-jsonrpc/lib/socket/connection.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-ws-jsonrpc/lib/socket/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-ws-jsonrpc/lib/logger.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-ws-jsonrpc/lib/connection.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/vscode-ws-jsonrpc/lib/index.js"], "sourcesContent": ["/**\n * Lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used to detect hot functions by number of calls within a span of milliseconds. */\nvar HOT_COUNT = 800,\n    HOT_SPAN = 16;\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    asyncTag = '[object AsyncFunction]',\n    boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    nullTag = '[object Null]',\n    objectTag = '[object Object]',\n    proxyTag = '[object Proxy]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    undefinedTag = '[object Undefined]',\n    weakMapTag = '[object WeakMap]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/** Used to identify `toStringTag` values of typed arrays. */\nvar typedArrayTags = {};\ntypedArrayTags[float32Tag] = typedArrayTags[float64Tag] =\ntypedArrayTags[int8Tag] = typedArrayTags[int16Tag] =\ntypedArrayTags[int32Tag] = typedArrayTags[uint8Tag] =\ntypedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] =\ntypedArrayTags[uint32Tag] = true;\ntypedArrayTags[argsTag] = typedArrayTags[arrayTag] =\ntypedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] =\ntypedArrayTags[dataViewTag] = typedArrayTags[dateTag] =\ntypedArrayTags[errorTag] = typedArrayTags[funcTag] =\ntypedArrayTags[mapTag] = typedArrayTags[numberTag] =\ntypedArrayTags[objectTag] = typedArrayTags[regexpTag] =\ntypedArrayTags[setTag] = typedArrayTags[stringTag] =\ntypedArrayTags[weakMapTag] = false;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Detect free variable `process` from Node.js. */\nvar freeProcess = moduleExports && freeGlobal.process;\n\n/** Used to access faster Node.js helpers. */\nvar nodeUtil = (function() {\n  try {\n    // Use `util.types` for Node.js 10+.\n    var types = freeModule && freeModule.require && freeModule.require('util').types;\n\n    if (types) {\n      return types;\n    }\n\n    // Legacy `process.binding('util')` for Node.js < 10.\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\n  } catch (e) {}\n}());\n\n/* Node.js helper references. */\nvar nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n\n/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0: return func.call(thisArg);\n    case 1: return func.call(thisArg, args[0]);\n    case 2: return func.call(thisArg, args[0], args[1]);\n    case 3: return func.call(thisArg, args[0], args[1], args[2]);\n  }\n  return func.apply(thisArg, args);\n}\n\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */\nfunction baseUnary(func) {\n  return function(value) {\n    return func(value);\n  };\n}\n\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype,\n    funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Used to infer the `Object` constructor. */\nvar objectCtorString = funcToString.call(Object);\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    Symbol = root.Symbol,\n    Uint8Array = root.Uint8Array,\n    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined,\n    getPrototype = overArg(Object.getPrototypeOf, Object),\n    objectCreate = Object.create,\n    propertyIsEnumerable = objectProto.propertyIsEnumerable,\n    splice = arrayProto.splice,\n    symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\nvar defineProperty = (function() {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}());\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined,\n    nativeMax = Math.max,\n    nativeNow = Date.now;\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map'),\n    nativeCreate = getNative(Object, 'create');\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = (function() {\n  function object() {}\n  return function(proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object;\n    object.prototype = undefined;\n    return result;\n  };\n}());\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n  this.size = 0;\n}\n\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);\n}\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n  this.size = 0;\n}\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  --this.size;\n  return true;\n}\n\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  return index < 0 ? undefined : data[index][1];\n}\n\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction listCacheHas(key) {\n  return assocIndexOf(this.__data__, key) > -1;\n}\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n}\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache;\n  this.size = 0;\n}\n\n/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  var data = this.__data__,\n      result = data['delete'](key);\n\n  this.size = data.size;\n  return result;\n}\n\n/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\n/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction stackHas(key) {\n  return this.__data__.has(key);\n}\n\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\nfunction stackSet(key, value) {\n  var data = this.__data__;\n  if (data instanceof ListCache) {\n    var pairs = data.__data__;\n    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n    data = this.__data__ = new MapCache(pairs);\n  }\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  var isArr = isArray(value),\n      isArg = !isArr && isArguments(value),\n      isBuff = !isArr && !isArg && isBuffer(value),\n      isType = !isArr && !isArg && !isBuff && isTypedArray(value),\n      skipIndexes = isArr || isArg || isBuff || isType,\n      result = skipIndexes ? baseTimes(value.length, String) : [],\n      length = result.length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (\n           // Safari 9 has enumerable `arguments.length` in strict mode.\n           key == 'length' ||\n           // Node.js 0.10 has enumerable non-index properties on buffers.\n           (isBuff && (key == 'offset' || key == 'parent')) ||\n           // PhantomJS 2 has enumerable non-index properties on typed arrays.\n           (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||\n           // Skip index properties.\n           isIndex(key, length)\n        ))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignMergeValue(object, key, value) {\n  if ((value !== undefined && !eq(object[key], value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\n\n/**\n * The base implementation of `baseForOwn` which iterates over `object`\n * properties returned by `keysFunc` and invokes `iteratee` for each property.\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\nvar baseFor = createBaseFor();\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */\nfunction baseIsArguments(value) {\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\n/**\n * The base implementation of `_.isTypedArray` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n */\nfunction baseIsTypedArray(value) {\n  return isObjectLike(value) &&\n    isLength(value.length) && !!typedArrayTags[baseGetTag(value)];\n}\n\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeysIn(object) {\n  if (!isObject(object)) {\n    return nativeKeysIn(object);\n  }\n  var isProto = isPrototype(object),\n      result = [];\n\n  for (var key in object) {\n    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.merge` without support for multiple sources.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} [customizer] The function to customize merged values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMerge(object, source, srcIndex, customizer, stack) {\n  if (object === source) {\n    return;\n  }\n  baseFor(source, function(srcValue, key) {\n    stack || (stack = new Stack);\n    if (isObject(srcValue)) {\n      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);\n    }\n    else {\n      var newValue = customizer\n        ? customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack)\n        : undefined;\n\n      if (newValue === undefined) {\n        newValue = srcValue;\n      }\n      assignMergeValue(object, key, newValue);\n    }\n  }, keysIn);\n}\n\n/**\n * A specialized version of `baseMerge` for arrays and objects which performs\n * deep merges and tracks traversed objects enabling objects with circular\n * references to be merged.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {string} key The key of the value to merge.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} mergeFunc The function to merge values.\n * @param {Function} [customizer] The function to customize assigned values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {\n  var objValue = safeGet(object, key),\n      srcValue = safeGet(source, key),\n      stacked = stack.get(srcValue);\n\n  if (stacked) {\n    assignMergeValue(object, key, stacked);\n    return;\n  }\n  var newValue = customizer\n    ? customizer(objValue, srcValue, (key + ''), object, source, stack)\n    : undefined;\n\n  var isCommon = newValue === undefined;\n\n  if (isCommon) {\n    var isArr = isArray(srcValue),\n        isBuff = !isArr && isBuffer(srcValue),\n        isTyped = !isArr && !isBuff && isTypedArray(srcValue);\n\n    newValue = srcValue;\n    if (isArr || isBuff || isTyped) {\n      if (isArray(objValue)) {\n        newValue = objValue;\n      }\n      else if (isArrayLikeObject(objValue)) {\n        newValue = copyArray(objValue);\n      }\n      else if (isBuff) {\n        isCommon = false;\n        newValue = cloneBuffer(srcValue, true);\n      }\n      else if (isTyped) {\n        isCommon = false;\n        newValue = cloneTypedArray(srcValue, true);\n      }\n      else {\n        newValue = [];\n      }\n    }\n    else if (isPlainObject(srcValue) || isArguments(srcValue)) {\n      newValue = objValue;\n      if (isArguments(objValue)) {\n        newValue = toPlainObject(objValue);\n      }\n      else if (!isObject(objValue) || isFunction(objValue)) {\n        newValue = initCloneObject(srcValue);\n      }\n    }\n    else {\n      isCommon = false;\n    }\n  }\n  if (isCommon) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, newValue);\n    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);\n    stack['delete'](srcValue);\n  }\n  assignMergeValue(object, key, newValue);\n}\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  return setToString(overRest(func, start, identity), func + '');\n}\n\n/**\n * The base implementation of `setToString` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar baseSetToString = !defineProperty ? identity : function(func, string) {\n  return defineProperty(func, 'toString', {\n    'configurable': true,\n    'enumerable': false,\n    'value': constant(string),\n    'writable': true\n  });\n};\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {Buffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var length = buffer.length,\n      result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n\n  buffer.copy(result);\n  return result;\n}\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\n/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n      length = source.length;\n\n  array || (array = Array(length));\n  while (++index < length) {\n    array[index] = source[index];\n  }\n  return array;\n}\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n  return object;\n}\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function(object, sources) {\n    var index = -1,\n        length = sources.length,\n        customizer = length > 1 ? sources[length - 1] : undefined,\n        guard = length > 2 ? sources[2] : undefined;\n\n    customizer = (assigner.length > 3 && typeof customizer == 'function')\n      ? (length--, customizer)\n      : undefined;\n\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\n\n/**\n * Creates a base function for methods like `_.forIn` and `_.forOwn`.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseFor(fromRight) {\n  return function(object, iteratee, keysFunc) {\n    var index = -1,\n        iterable = Object(object),\n        props = keysFunc(object),\n        length = props.length;\n\n    while (length--) {\n      var key = props[fromRight ? length : ++index];\n      if (iteratee(iterable[key], key, iterable) === false) {\n        break;\n      }\n    }\n    return object;\n  };\n}\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  var type = typeof value;\n  length = length == null ? MAX_SAFE_INTEGER : length;\n\n  return !!length &&\n    (type == 'number' ||\n      (type != 'symbol' && reIsUint.test(value))) &&\n        (value > -1 && value % 1 == 0 && value < length);\n}\n\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */\nfunction isIterateeCall(value, index, object) {\n  if (!isObject(object)) {\n    return false;\n  }\n  var type = typeof index;\n  if (type == 'number'\n        ? (isArrayLike(object) && isIndex(index, object.length))\n        : (type == 'string' && index in object)\n      ) {\n    return eq(object[index], value);\n  }\n  return false;\n}\n\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\n/**\n * This function is like\n * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * except that it includes inherited enumerable properties.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction nativeKeysIn(object) {\n  var result = [];\n  if (object != null) {\n    for (var key in Object(object)) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\n/**\n * A specialized version of `baseRest` which transforms the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @param {Function} transform The rest array transform.\n * @returns {Function} Returns the new function.\n */\nfunction overRest(func, start, transform) {\n  start = nativeMax(start === undefined ? (func.length - 1) : start, 0);\n  return function() {\n    var args = arguments,\n        index = -1,\n        length = nativeMax(args.length - start, 0),\n        array = Array(length);\n\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n    index = -1;\n    var otherArgs = Array(start + 1);\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n    otherArgs[start] = transform(array);\n    return apply(func, this, otherArgs);\n  };\n}\n\n/**\n * Gets the value at `key`, unless `key` is \"__proto__\" or \"constructor\".\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction safeGet(object, key) {\n  if (key === 'constructor' && typeof object[key] === 'function') {\n    return;\n  }\n\n  if (key == '__proto__') {\n    return;\n  }\n\n  return object[key];\n}\n\n/**\n * Sets the `toString` method of `func` to return `string`.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar setToString = shortOut(baseSetToString);\n\n/**\n * Creates a function that'll short out and invoke `identity` instead\n * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`\n * milliseconds.\n *\n * @private\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new shortable function.\n */\nfunction shortOut(func) {\n  var count = 0,\n      lastCalled = 0;\n\n  return function() {\n    var stamp = nativeNow(),\n        remaining = HOT_SPAN - (stamp - lastCalled);\n\n    lastCalled = stamp;\n    if (remaining > 0) {\n      if (++count >= HOT_COUNT) {\n        return arguments[0];\n      }\n    } else {\n      count = 0;\n    }\n    return func.apply(undefined, arguments);\n  };\n}\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nvar isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&\n    !propertyIsEnumerable.call(value, 'callee');\n};\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\nvar isBuffer = nativeIsBuffer || stubFalse;\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nfunction isPlainObject(value) {\n  if (!isObjectLike(value) || baseGetTag(value) != objectTag) {\n    return false;\n  }\n  var proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return typeof Ctor == 'function' && Ctor instanceof Ctor &&\n    funcToString.call(Ctor) == objectCtorString;\n}\n\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */\nvar isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\n\n/**\n * Converts `value` to a plain object flattening inherited enumerable string\n * keyed properties of `value` to own properties of the plain object.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {Object} Returns the converted plain object.\n * @example\n *\n * function Foo() {\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.assign({ 'a': 1 }, new Foo);\n * // => { 'a': 1, 'b': 2 }\n *\n * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));\n * // => { 'a': 1, 'b': 2, 'c': 3 }\n */\nfunction toPlainObject(value) {\n  return copyObject(value, keysIn(value));\n}\n\n/**\n * Creates an array of the own and inherited enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keysIn(new Foo);\n * // => ['a', 'b', 'c'] (iteration order is not guaranteed)\n */\nfunction keysIn(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);\n}\n\n/**\n * This method is like `_.merge` except that it accepts `customizer` which\n * is invoked to produce the merged values of the destination and source\n * properties. If `customizer` returns `undefined`, merging is handled by the\n * method instead. The `customizer` is invoked with six arguments:\n * (objValue, srcValue, key, object, source, stack).\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} sources The source objects.\n * @param {Function} customizer The function to customize assigned values.\n * @returns {Object} Returns `object`.\n * @example\n *\n * function customizer(objValue, srcValue) {\n *   if (_.isArray(objValue)) {\n *     return objValue.concat(srcValue);\n *   }\n * }\n *\n * var object = { 'a': [1], 'b': [2] };\n * var other = { 'a': [3], 'b': [4] };\n *\n * _.mergeWith(object, other, customizer);\n * // => { 'a': [1, 3], 'b': [2, 4] }\n */\nvar mergeWith = createAssigner(function(object, source, srcIndex, customizer) {\n  baseMerge(object, source, srcIndex, customizer);\n});\n\n/**\n * Creates a function that returns `value`.\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Util\n * @param {*} value The value to return from the new function.\n * @returns {Function} Returns the new constant function.\n * @example\n *\n * var objects = _.times(2, _.constant({ 'a': 1 }));\n *\n * console.log(objects);\n * // => [{ 'a': 1 }, { 'a': 1 }]\n *\n * console.log(objects[0] === objects[1]);\n * // => true\n */\nfunction constant(value) {\n  return function() {\n    return value;\n  };\n}\n\n/**\n * This method returns the first argument it receives.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {*} value Any value.\n * @returns {*} Returns `value`.\n * @example\n *\n * var object = { 'a': 1 };\n *\n * console.log(_.identity(object) === object);\n * // => true\n */\nfunction identity(value) {\n  return value;\n}\n\n/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */\nfunction stubFalse() {\n  return false;\n}\n\nmodule.exports = mergeWith;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createMessageConnection = exports.BrowserMessageWriter = exports.BrowserMessageReader = void 0;\nconst ril_1 = require(\"./ril\");\n// Install the browser runtime abstract.\nril_1.default.install();\nconst api_1 = require(\"../common/api\");\n__exportStar(require(\"../common/api\"), exports);\nclass BrowserMessageReader extends api_1.AbstractMessageReader {\n    constructor(port) {\n        super();\n        this._onData = new api_1.Emitter();\n        this._messageListener = (event) => {\n            this._onData.fire(event.data);\n        };\n        port.addEventListener('error', (event) => this.fireError(event));\n        port.onmessage = this._messageListener;\n    }\n    listen(callback) {\n        return this._onData.event(callback);\n    }\n}\nexports.BrowserMessageReader = BrowserMessageReader;\nclass BrowserMessageWriter extends api_1.AbstractMessageWriter {\n    constructor(port) {\n        super();\n        this.port = port;\n        this.errorCount = 0;\n        port.addEventListener('error', (event) => this.fireError(event));\n    }\n    write(msg) {\n        try {\n            this.port.postMessage(msg);\n            return Promise.resolve();\n        }\n        catch (error) {\n            this.handleError(error, msg);\n            return Promise.reject(error);\n        }\n    }\n    handleError(error, msg) {\n        this.errorCount++;\n        this.fireError(error, msg, this.errorCount);\n    }\n    end() {\n    }\n}\nexports.BrowserMessageWriter = BrowserMessageWriter;\nfunction createMessageConnection(reader, writer, logger, options) {\n    if (logger === undefined) {\n        logger = api_1.NullLogger;\n    }\n    if (api_1.ConnectionStrategy.is(options)) {\n        options = { connectionStrategy: options };\n    }\n    return (0, api_1.createMessageConnection)(reader, writer, logger, options);\n}\nexports.createMessageConnection = createMessageConnection;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst api_1 = require(\"../common/api\");\nclass MessageBuffer extends api_1.AbstractMessageBuffer {\n    constructor(encoding = 'utf-8') {\n        super(encoding);\n        this.asciiDecoder = new TextDecoder('ascii');\n    }\n    emptyBuffer() {\n        return MessageBuffer.emptyBuffer;\n    }\n    fromString(value, _encoding) {\n        return (new TextEncoder()).encode(value);\n    }\n    toString(value, encoding) {\n        if (encoding === 'ascii') {\n            return this.asciiDecoder.decode(value);\n        }\n        else {\n            return (new TextDecoder(encoding)).decode(value);\n        }\n    }\n    asNative(buffer, length) {\n        if (length === undefined) {\n            return buffer;\n        }\n        else {\n            return buffer.slice(0, length);\n        }\n    }\n    allocNative(length) {\n        return new Uint8Array(length);\n    }\n}\nMessageBuffer.emptyBuffer = new Uint8Array(0);\nclass ReadableStreamWrapper {\n    constructor(socket) {\n        this.socket = socket;\n        this._onData = new api_1.Emitter();\n        this._messageListener = (event) => {\n            const blob = event.data;\n            blob.arrayBuffer().then((buffer) => {\n                this._onData.fire(new Uint8Array(buffer));\n            }, () => {\n                (0, api_1.RAL)().console.error(`Converting blob to array buffer failed.`);\n            });\n        };\n        this.socket.addEventListener('message', this._messageListener);\n    }\n    onClose(listener) {\n        this.socket.addEventListener('close', listener);\n        return api_1.Disposable.create(() => this.socket.removeEventListener('close', listener));\n    }\n    onError(listener) {\n        this.socket.addEventListener('error', listener);\n        return api_1.Disposable.create(() => this.socket.removeEventListener('error', listener));\n    }\n    onEnd(listener) {\n        this.socket.addEventListener('end', listener);\n        return api_1.Disposable.create(() => this.socket.removeEventListener('end', listener));\n    }\n    onData(listener) {\n        return this._onData.event(listener);\n    }\n}\nclass WritableStreamWrapper {\n    constructor(socket) {\n        this.socket = socket;\n    }\n    onClose(listener) {\n        this.socket.addEventListener('close', listener);\n        return api_1.Disposable.create(() => this.socket.removeEventListener('close', listener));\n    }\n    onError(listener) {\n        this.socket.addEventListener('error', listener);\n        return api_1.Disposable.create(() => this.socket.removeEventListener('error', listener));\n    }\n    onEnd(listener) {\n        this.socket.addEventListener('end', listener);\n        return api_1.Disposable.create(() => this.socket.removeEventListener('end', listener));\n    }\n    write(data, encoding) {\n        if (typeof data === 'string') {\n            if (encoding !== undefined && encoding !== 'utf-8') {\n                throw new Error(`In a Browser environments only utf-8 text encoding is supported. But got encoding: ${encoding}`);\n            }\n            this.socket.send(data);\n        }\n        else {\n            this.socket.send(data);\n        }\n        return Promise.resolve();\n    }\n    end() {\n        this.socket.close();\n    }\n}\nconst _textEncoder = new TextEncoder();\nconst _ril = Object.freeze({\n    messageBuffer: Object.freeze({\n        create: (encoding) => new MessageBuffer(encoding)\n    }),\n    applicationJson: Object.freeze({\n        encoder: Object.freeze({\n            name: 'application/json',\n            encode: (msg, options) => {\n                if (options.charset !== 'utf-8') {\n                    throw new Error(`In a Browser environments only utf-8 text encoding is supported. But got encoding: ${options.charset}`);\n                }\n                return Promise.resolve(_textEncoder.encode(JSON.stringify(msg, undefined, 0)));\n            }\n        }),\n        decoder: Object.freeze({\n            name: 'application/json',\n            decode: (buffer, options) => {\n                if (!(buffer instanceof Uint8Array)) {\n                    throw new Error(`In a Browser environments only Uint8Arrays are supported.`);\n                }\n                return Promise.resolve(JSON.parse(new TextDecoder(options.charset).decode(buffer)));\n            }\n        })\n    }),\n    stream: Object.freeze({\n        asReadableStream: (socket) => new ReadableStreamWrapper(socket),\n        asWritableStream: (socket) => new WritableStreamWrapper(socket)\n    }),\n    console: console,\n    timer: Object.freeze({\n        setTimeout(callback, ms, ...args) {\n            const handle = setTimeout(callback, ms, ...args);\n            return { dispose: () => clearTimeout(handle) };\n        },\n        setImmediate(callback, ...args) {\n            const handle = setTimeout(callback, 0, ...args);\n            return { dispose: () => clearTimeout(handle) };\n        },\n        setInterval(callback, ms, ...args) {\n            const handle = setInterval(callback, ms, ...args);\n            return { dispose: () => clearInterval(handle) };\n        },\n    })\n});\nfunction RIL() {\n    return _ril;\n}\n(function (RIL) {\n    function install() {\n        api_1.RAL.install(_ril);\n    }\n    RIL.install = install;\n})(RIL || (RIL = {}));\nexports.default = RIL;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n/// <reference path=\"../../typings/thenable.d.ts\" />\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ProgressType = exports.ProgressToken = exports.createMessageConnection = exports.NullLogger = exports.ConnectionOptions = exports.ConnectionStrategy = exports.AbstractMessageBuffer = exports.WriteableStreamMessageWriter = exports.AbstractMessageWriter = exports.MessageWriter = exports.ReadableStreamMessageReader = exports.AbstractMessageReader = exports.MessageReader = exports.SharedArrayReceiverStrategy = exports.SharedArraySenderStrategy = exports.CancellationToken = exports.CancellationTokenSource = exports.Emitter = exports.Event = exports.Disposable = exports.LRUCache = exports.Touch = exports.LinkedMap = exports.ParameterStructures = exports.NotificationType9 = exports.NotificationType8 = exports.NotificationType7 = exports.NotificationType6 = exports.NotificationType5 = exports.NotificationType4 = exports.NotificationType3 = exports.NotificationType2 = exports.NotificationType1 = exports.NotificationType0 = exports.NotificationType = exports.ErrorCodes = exports.ResponseError = exports.RequestType9 = exports.RequestType8 = exports.RequestType7 = exports.RequestType6 = exports.RequestType5 = exports.RequestType4 = exports.RequestType3 = exports.RequestType2 = exports.RequestType1 = exports.RequestType0 = exports.RequestType = exports.Message = exports.RAL = void 0;\nexports.MessageStrategy = exports.CancellationStrategy = exports.CancellationSenderStrategy = exports.CancellationReceiverStrategy = exports.ConnectionError = exports.ConnectionErrors = exports.LogTraceNotification = exports.SetTraceNotification = exports.TraceFormat = exports.TraceValues = exports.Trace = void 0;\nconst messages_1 = require(\"./messages\");\nObject.defineProperty(exports, \"Message\", { enumerable: true, get: function () { return messages_1.Message; } });\nObject.defineProperty(exports, \"RequestType\", { enumerable: true, get: function () { return messages_1.RequestType; } });\nObject.defineProperty(exports, \"RequestType0\", { enumerable: true, get: function () { return messages_1.RequestType0; } });\nObject.defineProperty(exports, \"RequestType1\", { enumerable: true, get: function () { return messages_1.RequestType1; } });\nObject.defineProperty(exports, \"RequestType2\", { enumerable: true, get: function () { return messages_1.RequestType2; } });\nObject.defineProperty(exports, \"RequestType3\", { enumerable: true, get: function () { return messages_1.RequestType3; } });\nObject.defineProperty(exports, \"RequestType4\", { enumerable: true, get: function () { return messages_1.RequestType4; } });\nObject.defineProperty(exports, \"RequestType5\", { enumerable: true, get: function () { return messages_1.RequestType5; } });\nObject.defineProperty(exports, \"RequestType6\", { enumerable: true, get: function () { return messages_1.RequestType6; } });\nObject.defineProperty(exports, \"RequestType7\", { enumerable: true, get: function () { return messages_1.RequestType7; } });\nObject.defineProperty(exports, \"RequestType8\", { enumerable: true, get: function () { return messages_1.RequestType8; } });\nObject.defineProperty(exports, \"RequestType9\", { enumerable: true, get: function () { return messages_1.RequestType9; } });\nObject.defineProperty(exports, \"ResponseError\", { enumerable: true, get: function () { return messages_1.ResponseError; } });\nObject.defineProperty(exports, \"ErrorCodes\", { enumerable: true, get: function () { return messages_1.ErrorCodes; } });\nObject.defineProperty(exports, \"NotificationType\", { enumerable: true, get: function () { return messages_1.NotificationType; } });\nObject.defineProperty(exports, \"NotificationType0\", { enumerable: true, get: function () { return messages_1.NotificationType0; } });\nObject.defineProperty(exports, \"NotificationType1\", { enumerable: true, get: function () { return messages_1.NotificationType1; } });\nObject.defineProperty(exports, \"NotificationType2\", { enumerable: true, get: function () { return messages_1.NotificationType2; } });\nObject.defineProperty(exports, \"NotificationType3\", { enumerable: true, get: function () { return messages_1.NotificationType3; } });\nObject.defineProperty(exports, \"NotificationType4\", { enumerable: true, get: function () { return messages_1.NotificationType4; } });\nObject.defineProperty(exports, \"NotificationType5\", { enumerable: true, get: function () { return messages_1.NotificationType5; } });\nObject.defineProperty(exports, \"NotificationType6\", { enumerable: true, get: function () { return messages_1.NotificationType6; } });\nObject.defineProperty(exports, \"NotificationType7\", { enumerable: true, get: function () { return messages_1.NotificationType7; } });\nObject.defineProperty(exports, \"NotificationType8\", { enumerable: true, get: function () { return messages_1.NotificationType8; } });\nObject.defineProperty(exports, \"NotificationType9\", { enumerable: true, get: function () { return messages_1.NotificationType9; } });\nObject.defineProperty(exports, \"ParameterStructures\", { enumerable: true, get: function () { return messages_1.ParameterStructures; } });\nconst linkedMap_1 = require(\"./linkedMap\");\nObject.defineProperty(exports, \"LinkedMap\", { enumerable: true, get: function () { return linkedMap_1.LinkedMap; } });\nObject.defineProperty(exports, \"LRUCache\", { enumerable: true, get: function () { return linkedMap_1.LRUCache; } });\nObject.defineProperty(exports, \"Touch\", { enumerable: true, get: function () { return linkedMap_1.Touch; } });\nconst disposable_1 = require(\"./disposable\");\nObject.defineProperty(exports, \"Disposable\", { enumerable: true, get: function () { return disposable_1.Disposable; } });\nconst events_1 = require(\"./events\");\nObject.defineProperty(exports, \"Event\", { enumerable: true, get: function () { return events_1.Event; } });\nObject.defineProperty(exports, \"Emitter\", { enumerable: true, get: function () { return events_1.Emitter; } });\nconst cancellation_1 = require(\"./cancellation\");\nObject.defineProperty(exports, \"CancellationTokenSource\", { enumerable: true, get: function () { return cancellation_1.CancellationTokenSource; } });\nObject.defineProperty(exports, \"CancellationToken\", { enumerable: true, get: function () { return cancellation_1.CancellationToken; } });\nconst sharedArrayCancellation_1 = require(\"./sharedArrayCancellation\");\nObject.defineProperty(exports, \"SharedArraySenderStrategy\", { enumerable: true, get: function () { return sharedArrayCancellation_1.SharedArraySenderStrategy; } });\nObject.defineProperty(exports, \"SharedArrayReceiverStrategy\", { enumerable: true, get: function () { return sharedArrayCancellation_1.SharedArrayReceiverStrategy; } });\nconst messageReader_1 = require(\"./messageReader\");\nObject.defineProperty(exports, \"MessageReader\", { enumerable: true, get: function () { return messageReader_1.MessageReader; } });\nObject.defineProperty(exports, \"AbstractMessageReader\", { enumerable: true, get: function () { return messageReader_1.AbstractMessageReader; } });\nObject.defineProperty(exports, \"ReadableStreamMessageReader\", { enumerable: true, get: function () { return messageReader_1.ReadableStreamMessageReader; } });\nconst messageWriter_1 = require(\"./messageWriter\");\nObject.defineProperty(exports, \"MessageWriter\", { enumerable: true, get: function () { return messageWriter_1.MessageWriter; } });\nObject.defineProperty(exports, \"AbstractMessageWriter\", { enumerable: true, get: function () { return messageWriter_1.AbstractMessageWriter; } });\nObject.defineProperty(exports, \"WriteableStreamMessageWriter\", { enumerable: true, get: function () { return messageWriter_1.WriteableStreamMessageWriter; } });\nconst messageBuffer_1 = require(\"./messageBuffer\");\nObject.defineProperty(exports, \"AbstractMessageBuffer\", { enumerable: true, get: function () { return messageBuffer_1.AbstractMessageBuffer; } });\nconst connection_1 = require(\"./connection\");\nObject.defineProperty(exports, \"ConnectionStrategy\", { enumerable: true, get: function () { return connection_1.ConnectionStrategy; } });\nObject.defineProperty(exports, \"ConnectionOptions\", { enumerable: true, get: function () { return connection_1.ConnectionOptions; } });\nObject.defineProperty(exports, \"NullLogger\", { enumerable: true, get: function () { return connection_1.NullLogger; } });\nObject.defineProperty(exports, \"createMessageConnection\", { enumerable: true, get: function () { return connection_1.createMessageConnection; } });\nObject.defineProperty(exports, \"ProgressToken\", { enumerable: true, get: function () { return connection_1.ProgressToken; } });\nObject.defineProperty(exports, \"ProgressType\", { enumerable: true, get: function () { return connection_1.ProgressType; } });\nObject.defineProperty(exports, \"Trace\", { enumerable: true, get: function () { return connection_1.Trace; } });\nObject.defineProperty(exports, \"TraceValues\", { enumerable: true, get: function () { return connection_1.TraceValues; } });\nObject.defineProperty(exports, \"TraceFormat\", { enumerable: true, get: function () { return connection_1.TraceFormat; } });\nObject.defineProperty(exports, \"SetTraceNotification\", { enumerable: true, get: function () { return connection_1.SetTraceNotification; } });\nObject.defineProperty(exports, \"LogTraceNotification\", { enumerable: true, get: function () { return connection_1.LogTraceNotification; } });\nObject.defineProperty(exports, \"ConnectionErrors\", { enumerable: true, get: function () { return connection_1.ConnectionErrors; } });\nObject.defineProperty(exports, \"ConnectionError\", { enumerable: true, get: function () { return connection_1.ConnectionError; } });\nObject.defineProperty(exports, \"CancellationReceiverStrategy\", { enumerable: true, get: function () { return connection_1.CancellationReceiverStrategy; } });\nObject.defineProperty(exports, \"CancellationSenderStrategy\", { enumerable: true, get: function () { return connection_1.CancellationSenderStrategy; } });\nObject.defineProperty(exports, \"CancellationStrategy\", { enumerable: true, get: function () { return connection_1.CancellationStrategy; } });\nObject.defineProperty(exports, \"MessageStrategy\", { enumerable: true, get: function () { return connection_1.MessageStrategy; } });\nconst ral_1 = require(\"./ral\");\nexports.RAL = ral_1.default;\n", "\"use strict\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CancellationTokenSource = exports.CancellationToken = void 0;\nconst ral_1 = require(\"./ral\");\nconst Is = require(\"./is\");\nconst events_1 = require(\"./events\");\nvar CancellationToken;\n(function (CancellationToken) {\n    CancellationToken.None = Object.freeze({\n        isCancellationRequested: false,\n        onCancellationRequested: events_1.Event.None\n    });\n    CancellationToken.Cancelled = Object.freeze({\n        isCancellationRequested: true,\n        onCancellationRequested: events_1.Event.None\n    });\n    function is(value) {\n        const candidate = value;\n        return candidate && (candidate === CancellationToken.None\n            || candidate === CancellationToken.Cancelled\n            || (Is.boolean(candidate.isCancellationRequested) && !!candidate.onCancellationRequested));\n    }\n    CancellationToken.is = is;\n})(CancellationToken || (exports.CancellationToken = CancellationToken = {}));\nconst shortcutEvent = Object.freeze(function (callback, context) {\n    const handle = (0, ral_1.default)().timer.setTimeout(callback.bind(context), 0);\n    return { dispose() { handle.dispose(); } };\n});\nclass MutableToken {\n    constructor() {\n        this._isCancelled = false;\n    }\n    cancel() {\n        if (!this._isCancelled) {\n            this._isCancelled = true;\n            if (this._emitter) {\n                this._emitter.fire(undefined);\n                this.dispose();\n            }\n        }\n    }\n    get isCancellationRequested() {\n        return this._isCancelled;\n    }\n    get onCancellationRequested() {\n        if (this._isCancelled) {\n            return shortcutEvent;\n        }\n        if (!this._emitter) {\n            this._emitter = new events_1.Emitter();\n        }\n        return this._emitter.event;\n    }\n    dispose() {\n        if (this._emitter) {\n            this._emitter.dispose();\n            this._emitter = undefined;\n        }\n    }\n}\nclass CancellationTokenSource {\n    get token() {\n        if (!this._token) {\n            // be lazy and create the token only when\n            // actually needed\n            this._token = new MutableToken();\n        }\n        return this._token;\n    }\n    cancel() {\n        if (!this._token) {\n            // save an object by returning the default\n            // cancelled token when cancellation happens\n            // before someone asks for the token\n            this._token = CancellationToken.Cancelled;\n        }\n        else {\n            this._token.cancel();\n        }\n    }\n    dispose() {\n        if (!this._token) {\n            // ensure to initialize with an empty token if we had none\n            this._token = CancellationToken.None;\n        }\n        else if (this._token instanceof MutableToken) {\n            // actually dispose\n            this._token.dispose();\n        }\n    }\n}\nexports.CancellationTokenSource = CancellationTokenSource;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createMessageConnection = exports.ConnectionOptions = exports.MessageStrategy = exports.CancellationStrategy = exports.CancellationSenderStrategy = exports.CancellationReceiverStrategy = exports.RequestCancellationReceiverStrategy = exports.IdCancellationReceiverStrategy = exports.ConnectionStrategy = exports.ConnectionError = exports.ConnectionErrors = exports.LogTraceNotification = exports.SetTraceNotification = exports.TraceFormat = exports.TraceValues = exports.Trace = exports.NullLogger = exports.ProgressType = exports.ProgressToken = void 0;\nconst ral_1 = require(\"./ral\");\nconst Is = require(\"./is\");\nconst messages_1 = require(\"./messages\");\nconst linkedMap_1 = require(\"./linkedMap\");\nconst events_1 = require(\"./events\");\nconst cancellation_1 = require(\"./cancellation\");\nvar CancelNotification;\n(function (CancelNotification) {\n    CancelNotification.type = new messages_1.NotificationType('$/cancelRequest');\n})(CancelNotification || (CancelNotification = {}));\nvar ProgressToken;\n(function (ProgressToken) {\n    function is(value) {\n        return typeof value === 'string' || typeof value === 'number';\n    }\n    ProgressToken.is = is;\n})(ProgressToken || (exports.ProgressToken = ProgressToken = {}));\nvar ProgressNotification;\n(function (ProgressNotification) {\n    ProgressNotification.type = new messages_1.NotificationType('$/progress');\n})(ProgressNotification || (ProgressNotification = {}));\nclass ProgressType {\n    constructor() {\n    }\n}\nexports.ProgressType = ProgressType;\nvar StarRequestHandler;\n(function (StarRequestHandler) {\n    function is(value) {\n        return Is.func(value);\n    }\n    StarRequestHandler.is = is;\n})(StarRequestHandler || (StarRequestHandler = {}));\nexports.NullLogger = Object.freeze({\n    error: () => { },\n    warn: () => { },\n    info: () => { },\n    log: () => { }\n});\nvar Trace;\n(function (Trace) {\n    Trace[Trace[\"Off\"] = 0] = \"Off\";\n    Trace[Trace[\"Messages\"] = 1] = \"Messages\";\n    Trace[Trace[\"Compact\"] = 2] = \"Compact\";\n    Trace[Trace[\"Verbose\"] = 3] = \"Verbose\";\n})(Trace || (exports.Trace = Trace = {}));\nvar TraceValues;\n(function (TraceValues) {\n    /**\n     * Turn tracing off.\n     */\n    TraceValues.Off = 'off';\n    /**\n     * Trace messages only.\n     */\n    TraceValues.Messages = 'messages';\n    /**\n     * Compact message tracing.\n     */\n    TraceValues.Compact = 'compact';\n    /**\n     * Verbose message tracing.\n     */\n    TraceValues.Verbose = 'verbose';\n})(TraceValues || (exports.TraceValues = TraceValues = {}));\n(function (Trace) {\n    function fromString(value) {\n        if (!Is.string(value)) {\n            return Trace.Off;\n        }\n        value = value.toLowerCase();\n        switch (value) {\n            case 'off':\n                return Trace.Off;\n            case 'messages':\n                return Trace.Messages;\n            case 'compact':\n                return Trace.Compact;\n            case 'verbose':\n                return Trace.Verbose;\n            default:\n                return Trace.Off;\n        }\n    }\n    Trace.fromString = fromString;\n    function toString(value) {\n        switch (value) {\n            case Trace.Off:\n                return 'off';\n            case Trace.Messages:\n                return 'messages';\n            case Trace.Compact:\n                return 'compact';\n            case Trace.Verbose:\n                return 'verbose';\n            default:\n                return 'off';\n        }\n    }\n    Trace.toString = toString;\n})(Trace || (exports.Trace = Trace = {}));\nvar TraceFormat;\n(function (TraceFormat) {\n    TraceFormat[\"Text\"] = \"text\";\n    TraceFormat[\"JSON\"] = \"json\";\n})(TraceFormat || (exports.TraceFormat = TraceFormat = {}));\n(function (TraceFormat) {\n    function fromString(value) {\n        if (!Is.string(value)) {\n            return TraceFormat.Text;\n        }\n        value = value.toLowerCase();\n        if (value === 'json') {\n            return TraceFormat.JSON;\n        }\n        else {\n            return TraceFormat.Text;\n        }\n    }\n    TraceFormat.fromString = fromString;\n})(TraceFormat || (exports.TraceFormat = TraceFormat = {}));\nvar SetTraceNotification;\n(function (SetTraceNotification) {\n    SetTraceNotification.type = new messages_1.NotificationType('$/setTrace');\n})(SetTraceNotification || (exports.SetTraceNotification = SetTraceNotification = {}));\nvar LogTraceNotification;\n(function (LogTraceNotification) {\n    LogTraceNotification.type = new messages_1.NotificationType('$/logTrace');\n})(LogTraceNotification || (exports.LogTraceNotification = LogTraceNotification = {}));\nvar ConnectionErrors;\n(function (ConnectionErrors) {\n    /**\n     * The connection is closed.\n     */\n    ConnectionErrors[ConnectionErrors[\"Closed\"] = 1] = \"Closed\";\n    /**\n     * The connection got disposed.\n     */\n    ConnectionErrors[ConnectionErrors[\"Disposed\"] = 2] = \"Disposed\";\n    /**\n     * The connection is already in listening mode.\n     */\n    ConnectionErrors[ConnectionErrors[\"AlreadyListening\"] = 3] = \"AlreadyListening\";\n})(ConnectionErrors || (exports.ConnectionErrors = ConnectionErrors = {}));\nclass ConnectionError extends Error {\n    constructor(code, message) {\n        super(message);\n        this.code = code;\n        Object.setPrototypeOf(this, ConnectionError.prototype);\n    }\n}\nexports.ConnectionError = ConnectionError;\nvar ConnectionStrategy;\n(function (ConnectionStrategy) {\n    function is(value) {\n        const candidate = value;\n        return candidate && Is.func(candidate.cancelUndispatched);\n    }\n    ConnectionStrategy.is = is;\n})(ConnectionStrategy || (exports.ConnectionStrategy = ConnectionStrategy = {}));\nvar IdCancellationReceiverStrategy;\n(function (IdCancellationReceiverStrategy) {\n    function is(value) {\n        const candidate = value;\n        return candidate && (candidate.kind === undefined || candidate.kind === 'id') && Is.func(candidate.createCancellationTokenSource) && (candidate.dispose === undefined || Is.func(candidate.dispose));\n    }\n    IdCancellationReceiverStrategy.is = is;\n})(IdCancellationReceiverStrategy || (exports.IdCancellationReceiverStrategy = IdCancellationReceiverStrategy = {}));\nvar RequestCancellationReceiverStrategy;\n(function (RequestCancellationReceiverStrategy) {\n    function is(value) {\n        const candidate = value;\n        return candidate && candidate.kind === 'request' && Is.func(candidate.createCancellationTokenSource) && (candidate.dispose === undefined || Is.func(candidate.dispose));\n    }\n    RequestCancellationReceiverStrategy.is = is;\n})(RequestCancellationReceiverStrategy || (exports.RequestCancellationReceiverStrategy = RequestCancellationReceiverStrategy = {}));\nvar CancellationReceiverStrategy;\n(function (CancellationReceiverStrategy) {\n    CancellationReceiverStrategy.Message = Object.freeze({\n        createCancellationTokenSource(_) {\n            return new cancellation_1.CancellationTokenSource();\n        }\n    });\n    function is(value) {\n        return IdCancellationReceiverStrategy.is(value) || RequestCancellationReceiverStrategy.is(value);\n    }\n    CancellationReceiverStrategy.is = is;\n})(CancellationReceiverStrategy || (exports.CancellationReceiverStrategy = CancellationReceiverStrategy = {}));\nvar CancellationSenderStrategy;\n(function (CancellationSenderStrategy) {\n    CancellationSenderStrategy.Message = Object.freeze({\n        sendCancellation(conn, id) {\n            return conn.sendNotification(CancelNotification.type, { id });\n        },\n        cleanup(_) { }\n    });\n    function is(value) {\n        const candidate = value;\n        return candidate && Is.func(candidate.sendCancellation) && Is.func(candidate.cleanup);\n    }\n    CancellationSenderStrategy.is = is;\n})(CancellationSenderStrategy || (exports.CancellationSenderStrategy = CancellationSenderStrategy = {}));\nvar CancellationStrategy;\n(function (CancellationStrategy) {\n    CancellationStrategy.Message = Object.freeze({\n        receiver: CancellationReceiverStrategy.Message,\n        sender: CancellationSenderStrategy.Message\n    });\n    function is(value) {\n        const candidate = value;\n        return candidate && CancellationReceiverStrategy.is(candidate.receiver) && CancellationSenderStrategy.is(candidate.sender);\n    }\n    CancellationStrategy.is = is;\n})(CancellationStrategy || (exports.CancellationStrategy = CancellationStrategy = {}));\nvar MessageStrategy;\n(function (MessageStrategy) {\n    function is(value) {\n        const candidate = value;\n        return candidate && Is.func(candidate.handleMessage);\n    }\n    MessageStrategy.is = is;\n})(MessageStrategy || (exports.MessageStrategy = MessageStrategy = {}));\nvar ConnectionOptions;\n(function (ConnectionOptions) {\n    function is(value) {\n        const candidate = value;\n        return candidate && (CancellationStrategy.is(candidate.cancellationStrategy) || ConnectionStrategy.is(candidate.connectionStrategy) || MessageStrategy.is(candidate.messageStrategy));\n    }\n    ConnectionOptions.is = is;\n})(ConnectionOptions || (exports.ConnectionOptions = ConnectionOptions = {}));\nvar ConnectionState;\n(function (ConnectionState) {\n    ConnectionState[ConnectionState[\"New\"] = 1] = \"New\";\n    ConnectionState[ConnectionState[\"Listening\"] = 2] = \"Listening\";\n    ConnectionState[ConnectionState[\"Closed\"] = 3] = \"Closed\";\n    ConnectionState[ConnectionState[\"Disposed\"] = 4] = \"Disposed\";\n})(ConnectionState || (ConnectionState = {}));\nfunction createMessageConnection(messageReader, messageWriter, _logger, options) {\n    const logger = _logger !== undefined ? _logger : exports.NullLogger;\n    let sequenceNumber = 0;\n    let notificationSequenceNumber = 0;\n    let unknownResponseSequenceNumber = 0;\n    const version = '2.0';\n    let starRequestHandler = undefined;\n    const requestHandlers = new Map();\n    let starNotificationHandler = undefined;\n    const notificationHandlers = new Map();\n    const progressHandlers = new Map();\n    let timer;\n    let messageQueue = new linkedMap_1.LinkedMap();\n    let responsePromises = new Map();\n    let knownCanceledRequests = new Set();\n    let requestTokens = new Map();\n    let trace = Trace.Off;\n    let traceFormat = TraceFormat.Text;\n    let tracer;\n    let state = ConnectionState.New;\n    const errorEmitter = new events_1.Emitter();\n    const closeEmitter = new events_1.Emitter();\n    const unhandledNotificationEmitter = new events_1.Emitter();\n    const unhandledProgressEmitter = new events_1.Emitter();\n    const disposeEmitter = new events_1.Emitter();\n    const cancellationStrategy = (options && options.cancellationStrategy) ? options.cancellationStrategy : CancellationStrategy.Message;\n    function createRequestQueueKey(id) {\n        if (id === null) {\n            throw new Error(`Can't send requests with id null since the response can't be correlated.`);\n        }\n        return 'req-' + id.toString();\n    }\n    function createResponseQueueKey(id) {\n        if (id === null) {\n            return 'res-unknown-' + (++unknownResponseSequenceNumber).toString();\n        }\n        else {\n            return 'res-' + id.toString();\n        }\n    }\n    function createNotificationQueueKey() {\n        return 'not-' + (++notificationSequenceNumber).toString();\n    }\n    function addMessageToQueue(queue, message) {\n        if (messages_1.Message.isRequest(message)) {\n            queue.set(createRequestQueueKey(message.id), message);\n        }\n        else if (messages_1.Message.isResponse(message)) {\n            queue.set(createResponseQueueKey(message.id), message);\n        }\n        else {\n            queue.set(createNotificationQueueKey(), message);\n        }\n    }\n    function cancelUndispatched(_message) {\n        return undefined;\n    }\n    function isListening() {\n        return state === ConnectionState.Listening;\n    }\n    function isClosed() {\n        return state === ConnectionState.Closed;\n    }\n    function isDisposed() {\n        return state === ConnectionState.Disposed;\n    }\n    function closeHandler() {\n        if (state === ConnectionState.New || state === ConnectionState.Listening) {\n            state = ConnectionState.Closed;\n            closeEmitter.fire(undefined);\n        }\n        // If the connection is disposed don't sent close events.\n    }\n    function readErrorHandler(error) {\n        errorEmitter.fire([error, undefined, undefined]);\n    }\n    function writeErrorHandler(data) {\n        errorEmitter.fire(data);\n    }\n    messageReader.onClose(closeHandler);\n    messageReader.onError(readErrorHandler);\n    messageWriter.onClose(closeHandler);\n    messageWriter.onError(writeErrorHandler);\n    function triggerMessageQueue() {\n        if (timer || messageQueue.size === 0) {\n            return;\n        }\n        timer = (0, ral_1.default)().timer.setImmediate(() => {\n            timer = undefined;\n            processMessageQueue();\n        });\n    }\n    function handleMessage(message) {\n        if (messages_1.Message.isRequest(message)) {\n            handleRequest(message);\n        }\n        else if (messages_1.Message.isNotification(message)) {\n            handleNotification(message);\n        }\n        else if (messages_1.Message.isResponse(message)) {\n            handleResponse(message);\n        }\n        else {\n            handleInvalidMessage(message);\n        }\n    }\n    function processMessageQueue() {\n        if (messageQueue.size === 0) {\n            return;\n        }\n        const message = messageQueue.shift();\n        try {\n            const messageStrategy = options?.messageStrategy;\n            if (MessageStrategy.is(messageStrategy)) {\n                messageStrategy.handleMessage(message, handleMessage);\n            }\n            else {\n                handleMessage(message);\n            }\n        }\n        finally {\n            triggerMessageQueue();\n        }\n    }\n    const callback = (message) => {\n        try {\n            // We have received a cancellation message. Check if the message is still in the queue\n            // and cancel it if allowed to do so.\n            if (messages_1.Message.isNotification(message) && message.method === CancelNotification.type.method) {\n                const cancelId = message.params.id;\n                const key = createRequestQueueKey(cancelId);\n                const toCancel = messageQueue.get(key);\n                if (messages_1.Message.isRequest(toCancel)) {\n                    const strategy = options?.connectionStrategy;\n                    const response = (strategy && strategy.cancelUndispatched) ? strategy.cancelUndispatched(toCancel, cancelUndispatched) : cancelUndispatched(toCancel);\n                    if (response && (response.error !== undefined || response.result !== undefined)) {\n                        messageQueue.delete(key);\n                        requestTokens.delete(cancelId);\n                        response.id = toCancel.id;\n                        traceSendingResponse(response, message.method, Date.now());\n                        messageWriter.write(response).catch(() => logger.error(`Sending response for canceled message failed.`));\n                        return;\n                    }\n                }\n                const cancellationToken = requestTokens.get(cancelId);\n                // The request is already running. Cancel the token\n                if (cancellationToken !== undefined) {\n                    cancellationToken.cancel();\n                    traceReceivedNotification(message);\n                    return;\n                }\n                else {\n                    // Remember the cancel but still queue the message to\n                    // clean up state in process message.\n                    knownCanceledRequests.add(cancelId);\n                }\n            }\n            addMessageToQueue(messageQueue, message);\n        }\n        finally {\n            triggerMessageQueue();\n        }\n    };\n    function handleRequest(requestMessage) {\n        if (isDisposed()) {\n            // we return here silently since we fired an event when the\n            // connection got disposed.\n            return;\n        }\n        function reply(resultOrError, method, startTime) {\n            const message = {\n                jsonrpc: version,\n                id: requestMessage.id\n            };\n            if (resultOrError instanceof messages_1.ResponseError) {\n                message.error = resultOrError.toJson();\n            }\n            else {\n                message.result = resultOrError === undefined ? null : resultOrError;\n            }\n            traceSendingResponse(message, method, startTime);\n            messageWriter.write(message).catch(() => logger.error(`Sending response failed.`));\n        }\n        function replyError(error, method, startTime) {\n            const message = {\n                jsonrpc: version,\n                id: requestMessage.id,\n                error: error.toJson()\n            };\n            traceSendingResponse(message, method, startTime);\n            messageWriter.write(message).catch(() => logger.error(`Sending response failed.`));\n        }\n        function replySuccess(result, method, startTime) {\n            // The JSON RPC defines that a response must either have a result or an error\n            // So we can't treat undefined as a valid response result.\n            if (result === undefined) {\n                result = null;\n            }\n            const message = {\n                jsonrpc: version,\n                id: requestMessage.id,\n                result: result\n            };\n            traceSendingResponse(message, method, startTime);\n            messageWriter.write(message).catch(() => logger.error(`Sending response failed.`));\n        }\n        traceReceivedRequest(requestMessage);\n        const element = requestHandlers.get(requestMessage.method);\n        let type;\n        let requestHandler;\n        if (element) {\n            type = element.type;\n            requestHandler = element.handler;\n        }\n        const startTime = Date.now();\n        if (requestHandler || starRequestHandler) {\n            const tokenKey = requestMessage.id ?? String(Date.now()); //\n            const cancellationSource = IdCancellationReceiverStrategy.is(cancellationStrategy.receiver)\n                ? cancellationStrategy.receiver.createCancellationTokenSource(tokenKey)\n                : cancellationStrategy.receiver.createCancellationTokenSource(requestMessage);\n            if (requestMessage.id !== null && knownCanceledRequests.has(requestMessage.id)) {\n                cancellationSource.cancel();\n            }\n            if (requestMessage.id !== null) {\n                requestTokens.set(tokenKey, cancellationSource);\n            }\n            try {\n                let handlerResult;\n                if (requestHandler) {\n                    if (requestMessage.params === undefined) {\n                        if (type !== undefined && type.numberOfParams !== 0) {\n                            replyError(new messages_1.ResponseError(messages_1.ErrorCodes.InvalidParams, `Request ${requestMessage.method} defines ${type.numberOfParams} params but received none.`), requestMessage.method, startTime);\n                            return;\n                        }\n                        handlerResult = requestHandler(cancellationSource.token);\n                    }\n                    else if (Array.isArray(requestMessage.params)) {\n                        if (type !== undefined && type.parameterStructures === messages_1.ParameterStructures.byName) {\n                            replyError(new messages_1.ResponseError(messages_1.ErrorCodes.InvalidParams, `Request ${requestMessage.method} defines parameters by name but received parameters by position`), requestMessage.method, startTime);\n                            return;\n                        }\n                        handlerResult = requestHandler(...requestMessage.params, cancellationSource.token);\n                    }\n                    else {\n                        if (type !== undefined && type.parameterStructures === messages_1.ParameterStructures.byPosition) {\n                            replyError(new messages_1.ResponseError(messages_1.ErrorCodes.InvalidParams, `Request ${requestMessage.method} defines parameters by position but received parameters by name`), requestMessage.method, startTime);\n                            return;\n                        }\n                        handlerResult = requestHandler(requestMessage.params, cancellationSource.token);\n                    }\n                }\n                else if (starRequestHandler) {\n                    handlerResult = starRequestHandler(requestMessage.method, requestMessage.params, cancellationSource.token);\n                }\n                const promise = handlerResult;\n                if (!handlerResult) {\n                    requestTokens.delete(tokenKey);\n                    replySuccess(handlerResult, requestMessage.method, startTime);\n                }\n                else if (promise.then) {\n                    promise.then((resultOrError) => {\n                        requestTokens.delete(tokenKey);\n                        reply(resultOrError, requestMessage.method, startTime);\n                    }, error => {\n                        requestTokens.delete(tokenKey);\n                        if (error instanceof messages_1.ResponseError) {\n                            replyError(error, requestMessage.method, startTime);\n                        }\n                        else if (error && Is.string(error.message)) {\n                            replyError(new messages_1.ResponseError(messages_1.ErrorCodes.InternalError, `Request ${requestMessage.method} failed with message: ${error.message}`), requestMessage.method, startTime);\n                        }\n                        else {\n                            replyError(new messages_1.ResponseError(messages_1.ErrorCodes.InternalError, `Request ${requestMessage.method} failed unexpectedly without providing any details.`), requestMessage.method, startTime);\n                        }\n                    });\n                }\n                else {\n                    requestTokens.delete(tokenKey);\n                    reply(handlerResult, requestMessage.method, startTime);\n                }\n            }\n            catch (error) {\n                requestTokens.delete(tokenKey);\n                if (error instanceof messages_1.ResponseError) {\n                    reply(error, requestMessage.method, startTime);\n                }\n                else if (error && Is.string(error.message)) {\n                    replyError(new messages_1.ResponseError(messages_1.ErrorCodes.InternalError, `Request ${requestMessage.method} failed with message: ${error.message}`), requestMessage.method, startTime);\n                }\n                else {\n                    replyError(new messages_1.ResponseError(messages_1.ErrorCodes.InternalError, `Request ${requestMessage.method} failed unexpectedly without providing any details.`), requestMessage.method, startTime);\n                }\n            }\n        }\n        else {\n            replyError(new messages_1.ResponseError(messages_1.ErrorCodes.MethodNotFound, `Unhandled method ${requestMessage.method}`), requestMessage.method, startTime);\n        }\n    }\n    function handleResponse(responseMessage) {\n        if (isDisposed()) {\n            // See handle request.\n            return;\n        }\n        if (responseMessage.id === null) {\n            if (responseMessage.error) {\n                logger.error(`Received response message without id: Error is: \\n${JSON.stringify(responseMessage.error, undefined, 4)}`);\n            }\n            else {\n                logger.error(`Received response message without id. No further error information provided.`);\n            }\n        }\n        else {\n            const key = responseMessage.id;\n            const responsePromise = responsePromises.get(key);\n            traceReceivedResponse(responseMessage, responsePromise);\n            if (responsePromise !== undefined) {\n                responsePromises.delete(key);\n                try {\n                    if (responseMessage.error) {\n                        const error = responseMessage.error;\n                        responsePromise.reject(new messages_1.ResponseError(error.code, error.message, error.data));\n                    }\n                    else if (responseMessage.result !== undefined) {\n                        responsePromise.resolve(responseMessage.result);\n                    }\n                    else {\n                        throw new Error('Should never happen.');\n                    }\n                }\n                catch (error) {\n                    if (error.message) {\n                        logger.error(`Response handler '${responsePromise.method}' failed with message: ${error.message}`);\n                    }\n                    else {\n                        logger.error(`Response handler '${responsePromise.method}' failed unexpectedly.`);\n                    }\n                }\n            }\n        }\n    }\n    function handleNotification(message) {\n        if (isDisposed()) {\n            // See handle request.\n            return;\n        }\n        let type = undefined;\n        let notificationHandler;\n        if (message.method === CancelNotification.type.method) {\n            const cancelId = message.params.id;\n            knownCanceledRequests.delete(cancelId);\n            traceReceivedNotification(message);\n            return;\n        }\n        else {\n            const element = notificationHandlers.get(message.method);\n            if (element) {\n                notificationHandler = element.handler;\n                type = element.type;\n            }\n        }\n        if (notificationHandler || starNotificationHandler) {\n            try {\n                traceReceivedNotification(message);\n                if (notificationHandler) {\n                    if (message.params === undefined) {\n                        if (type !== undefined) {\n                            if (type.numberOfParams !== 0 && type.parameterStructures !== messages_1.ParameterStructures.byName) {\n                                logger.error(`Notification ${message.method} defines ${type.numberOfParams} params but received none.`);\n                            }\n                        }\n                        notificationHandler();\n                    }\n                    else if (Array.isArray(message.params)) {\n                        // There are JSON-RPC libraries that send progress message as positional params although\n                        // specified as named. So convert them if this is the case.\n                        const params = message.params;\n                        if (message.method === ProgressNotification.type.method && params.length === 2 && ProgressToken.is(params[0])) {\n                            notificationHandler({ token: params[0], value: params[1] });\n                        }\n                        else {\n                            if (type !== undefined) {\n                                if (type.parameterStructures === messages_1.ParameterStructures.byName) {\n                                    logger.error(`Notification ${message.method} defines parameters by name but received parameters by position`);\n                                }\n                                if (type.numberOfParams !== message.params.length) {\n                                    logger.error(`Notification ${message.method} defines ${type.numberOfParams} params but received ${params.length} arguments`);\n                                }\n                            }\n                            notificationHandler(...params);\n                        }\n                    }\n                    else {\n                        if (type !== undefined && type.parameterStructures === messages_1.ParameterStructures.byPosition) {\n                            logger.error(`Notification ${message.method} defines parameters by position but received parameters by name`);\n                        }\n                        notificationHandler(message.params);\n                    }\n                }\n                else if (starNotificationHandler) {\n                    starNotificationHandler(message.method, message.params);\n                }\n            }\n            catch (error) {\n                if (error.message) {\n                    logger.error(`Notification handler '${message.method}' failed with message: ${error.message}`);\n                }\n                else {\n                    logger.error(`Notification handler '${message.method}' failed unexpectedly.`);\n                }\n            }\n        }\n        else {\n            unhandledNotificationEmitter.fire(message);\n        }\n    }\n    function handleInvalidMessage(message) {\n        if (!message) {\n            logger.error('Received empty message.');\n            return;\n        }\n        logger.error(`Received message which is neither a response nor a notification message:\\n${JSON.stringify(message, null, 4)}`);\n        // Test whether we find an id to reject the promise\n        const responseMessage = message;\n        if (Is.string(responseMessage.id) || Is.number(responseMessage.id)) {\n            const key = responseMessage.id;\n            const responseHandler = responsePromises.get(key);\n            if (responseHandler) {\n                responseHandler.reject(new Error('The received response has neither a result nor an error property.'));\n            }\n        }\n    }\n    function stringifyTrace(params) {\n        if (params === undefined || params === null) {\n            return undefined;\n        }\n        switch (trace) {\n            case Trace.Verbose:\n                return JSON.stringify(params, null, 4);\n            case Trace.Compact:\n                return JSON.stringify(params);\n            default:\n                return undefined;\n        }\n    }\n    function traceSendingRequest(message) {\n        if (trace === Trace.Off || !tracer) {\n            return;\n        }\n        if (traceFormat === TraceFormat.Text) {\n            let data = undefined;\n            if ((trace === Trace.Verbose || trace === Trace.Compact) && message.params) {\n                data = `Params: ${stringifyTrace(message.params)}\\n\\n`;\n            }\n            tracer.log(`Sending request '${message.method} - (${message.id})'.`, data);\n        }\n        else {\n            logLSPMessage('send-request', message);\n        }\n    }\n    function traceSendingNotification(message) {\n        if (trace === Trace.Off || !tracer) {\n            return;\n        }\n        if (traceFormat === TraceFormat.Text) {\n            let data = undefined;\n            if (trace === Trace.Verbose || trace === Trace.Compact) {\n                if (message.params) {\n                    data = `Params: ${stringifyTrace(message.params)}\\n\\n`;\n                }\n                else {\n                    data = 'No parameters provided.\\n\\n';\n                }\n            }\n            tracer.log(`Sending notification '${message.method}'.`, data);\n        }\n        else {\n            logLSPMessage('send-notification', message);\n        }\n    }\n    function traceSendingResponse(message, method, startTime) {\n        if (trace === Trace.Off || !tracer) {\n            return;\n        }\n        if (traceFormat === TraceFormat.Text) {\n            let data = undefined;\n            if (trace === Trace.Verbose || trace === Trace.Compact) {\n                if (message.error && message.error.data) {\n                    data = `Error data: ${stringifyTrace(message.error.data)}\\n\\n`;\n                }\n                else {\n                    if (message.result) {\n                        data = `Result: ${stringifyTrace(message.result)}\\n\\n`;\n                    }\n                    else if (message.error === undefined) {\n                        data = 'No result returned.\\n\\n';\n                    }\n                }\n            }\n            tracer.log(`Sending response '${method} - (${message.id})'. Processing request took ${Date.now() - startTime}ms`, data);\n        }\n        else {\n            logLSPMessage('send-response', message);\n        }\n    }\n    function traceReceivedRequest(message) {\n        if (trace === Trace.Off || !tracer) {\n            return;\n        }\n        if (traceFormat === TraceFormat.Text) {\n            let data = undefined;\n            if ((trace === Trace.Verbose || trace === Trace.Compact) && message.params) {\n                data = `Params: ${stringifyTrace(message.params)}\\n\\n`;\n            }\n            tracer.log(`Received request '${message.method} - (${message.id})'.`, data);\n        }\n        else {\n            logLSPMessage('receive-request', message);\n        }\n    }\n    function traceReceivedNotification(message) {\n        if (trace === Trace.Off || !tracer || message.method === LogTraceNotification.type.method) {\n            return;\n        }\n        if (traceFormat === TraceFormat.Text) {\n            let data = undefined;\n            if (trace === Trace.Verbose || trace === Trace.Compact) {\n                if (message.params) {\n                    data = `Params: ${stringifyTrace(message.params)}\\n\\n`;\n                }\n                else {\n                    data = 'No parameters provided.\\n\\n';\n                }\n            }\n            tracer.log(`Received notification '${message.method}'.`, data);\n        }\n        else {\n            logLSPMessage('receive-notification', message);\n        }\n    }\n    function traceReceivedResponse(message, responsePromise) {\n        if (trace === Trace.Off || !tracer) {\n            return;\n        }\n        if (traceFormat === TraceFormat.Text) {\n            let data = undefined;\n            if (trace === Trace.Verbose || trace === Trace.Compact) {\n                if (message.error && message.error.data) {\n                    data = `Error data: ${stringifyTrace(message.error.data)}\\n\\n`;\n                }\n                else {\n                    if (message.result) {\n                        data = `Result: ${stringifyTrace(message.result)}\\n\\n`;\n                    }\n                    else if (message.error === undefined) {\n                        data = 'No result returned.\\n\\n';\n                    }\n                }\n            }\n            if (responsePromise) {\n                const error = message.error ? ` Request failed: ${message.error.message} (${message.error.code}).` : '';\n                tracer.log(`Received response '${responsePromise.method} - (${message.id})' in ${Date.now() - responsePromise.timerStart}ms.${error}`, data);\n            }\n            else {\n                tracer.log(`Received response ${message.id} without active response promise.`, data);\n            }\n        }\n        else {\n            logLSPMessage('receive-response', message);\n        }\n    }\n    function logLSPMessage(type, message) {\n        if (!tracer || trace === Trace.Off) {\n            return;\n        }\n        const lspMessage = {\n            isLSPMessage: true,\n            type,\n            message,\n            timestamp: Date.now()\n        };\n        tracer.log(lspMessage);\n    }\n    function throwIfClosedOrDisposed() {\n        if (isClosed()) {\n            throw new ConnectionError(ConnectionErrors.Closed, 'Connection is closed.');\n        }\n        if (isDisposed()) {\n            throw new ConnectionError(ConnectionErrors.Disposed, 'Connection is disposed.');\n        }\n    }\n    function throwIfListening() {\n        if (isListening()) {\n            throw new ConnectionError(ConnectionErrors.AlreadyListening, 'Connection is already listening');\n        }\n    }\n    function throwIfNotListening() {\n        if (!isListening()) {\n            throw new Error('Call listen() first.');\n        }\n    }\n    function undefinedToNull(param) {\n        if (param === undefined) {\n            return null;\n        }\n        else {\n            return param;\n        }\n    }\n    function nullToUndefined(param) {\n        if (param === null) {\n            return undefined;\n        }\n        else {\n            return param;\n        }\n    }\n    function isNamedParam(param) {\n        return param !== undefined && param !== null && !Array.isArray(param) && typeof param === 'object';\n    }\n    function computeSingleParam(parameterStructures, param) {\n        switch (parameterStructures) {\n            case messages_1.ParameterStructures.auto:\n                if (isNamedParam(param)) {\n                    return nullToUndefined(param);\n                }\n                else {\n                    return [undefinedToNull(param)];\n                }\n            case messages_1.ParameterStructures.byName:\n                if (!isNamedParam(param)) {\n                    throw new Error(`Received parameters by name but param is not an object literal.`);\n                }\n                return nullToUndefined(param);\n            case messages_1.ParameterStructures.byPosition:\n                return [undefinedToNull(param)];\n            default:\n                throw new Error(`Unknown parameter structure ${parameterStructures.toString()}`);\n        }\n    }\n    function computeMessageParams(type, params) {\n        let result;\n        const numberOfParams = type.numberOfParams;\n        switch (numberOfParams) {\n            case 0:\n                result = undefined;\n                break;\n            case 1:\n                result = computeSingleParam(type.parameterStructures, params[0]);\n                break;\n            default:\n                result = [];\n                for (let i = 0; i < params.length && i < numberOfParams; i++) {\n                    result.push(undefinedToNull(params[i]));\n                }\n                if (params.length < numberOfParams) {\n                    for (let i = params.length; i < numberOfParams; i++) {\n                        result.push(null);\n                    }\n                }\n                break;\n        }\n        return result;\n    }\n    const connection = {\n        sendNotification: (type, ...args) => {\n            throwIfClosedOrDisposed();\n            let method;\n            let messageParams;\n            if (Is.string(type)) {\n                method = type;\n                const first = args[0];\n                let paramStart = 0;\n                let parameterStructures = messages_1.ParameterStructures.auto;\n                if (messages_1.ParameterStructures.is(first)) {\n                    paramStart = 1;\n                    parameterStructures = first;\n                }\n                let paramEnd = args.length;\n                const numberOfParams = paramEnd - paramStart;\n                switch (numberOfParams) {\n                    case 0:\n                        messageParams = undefined;\n                        break;\n                    case 1:\n                        messageParams = computeSingleParam(parameterStructures, args[paramStart]);\n                        break;\n                    default:\n                        if (parameterStructures === messages_1.ParameterStructures.byName) {\n                            throw new Error(`Received ${numberOfParams} parameters for 'by Name' notification parameter structure.`);\n                        }\n                        messageParams = args.slice(paramStart, paramEnd).map(value => undefinedToNull(value));\n                        break;\n                }\n            }\n            else {\n                const params = args;\n                method = type.method;\n                messageParams = computeMessageParams(type, params);\n            }\n            const notificationMessage = {\n                jsonrpc: version,\n                method: method,\n                params: messageParams\n            };\n            traceSendingNotification(notificationMessage);\n            return messageWriter.write(notificationMessage).catch((error) => {\n                logger.error(`Sending notification failed.`);\n                throw error;\n            });\n        },\n        onNotification: (type, handler) => {\n            throwIfClosedOrDisposed();\n            let method;\n            if (Is.func(type)) {\n                starNotificationHandler = type;\n            }\n            else if (handler) {\n                if (Is.string(type)) {\n                    method = type;\n                    notificationHandlers.set(type, { type: undefined, handler });\n                }\n                else {\n                    method = type.method;\n                    notificationHandlers.set(type.method, { type, handler });\n                }\n            }\n            return {\n                dispose: () => {\n                    if (method !== undefined) {\n                        notificationHandlers.delete(method);\n                    }\n                    else {\n                        starNotificationHandler = undefined;\n                    }\n                }\n            };\n        },\n        onProgress: (_type, token, handler) => {\n            if (progressHandlers.has(token)) {\n                throw new Error(`Progress handler for token ${token} already registered`);\n            }\n            progressHandlers.set(token, handler);\n            return {\n                dispose: () => {\n                    progressHandlers.delete(token);\n                }\n            };\n        },\n        sendProgress: (_type, token, value) => {\n            // This should not await but simple return to ensure that we don't have another\n            // async scheduling. Otherwise one send could overtake another send.\n            return connection.sendNotification(ProgressNotification.type, { token, value });\n        },\n        onUnhandledProgress: unhandledProgressEmitter.event,\n        sendRequest: (type, ...args) => {\n            throwIfClosedOrDisposed();\n            throwIfNotListening();\n            let method;\n            let messageParams;\n            let token = undefined;\n            if (Is.string(type)) {\n                method = type;\n                const first = args[0];\n                const last = args[args.length - 1];\n                let paramStart = 0;\n                let parameterStructures = messages_1.ParameterStructures.auto;\n                if (messages_1.ParameterStructures.is(first)) {\n                    paramStart = 1;\n                    parameterStructures = first;\n                }\n                let paramEnd = args.length;\n                if (cancellation_1.CancellationToken.is(last)) {\n                    paramEnd = paramEnd - 1;\n                    token = last;\n                }\n                const numberOfParams = paramEnd - paramStart;\n                switch (numberOfParams) {\n                    case 0:\n                        messageParams = undefined;\n                        break;\n                    case 1:\n                        messageParams = computeSingleParam(parameterStructures, args[paramStart]);\n                        break;\n                    default:\n                        if (parameterStructures === messages_1.ParameterStructures.byName) {\n                            throw new Error(`Received ${numberOfParams} parameters for 'by Name' request parameter structure.`);\n                        }\n                        messageParams = args.slice(paramStart, paramEnd).map(value => undefinedToNull(value));\n                        break;\n                }\n            }\n            else {\n                const params = args;\n                method = type.method;\n                messageParams = computeMessageParams(type, params);\n                const numberOfParams = type.numberOfParams;\n                token = cancellation_1.CancellationToken.is(params[numberOfParams]) ? params[numberOfParams] : undefined;\n            }\n            const id = sequenceNumber++;\n            let disposable;\n            if (token) {\n                disposable = token.onCancellationRequested(() => {\n                    const p = cancellationStrategy.sender.sendCancellation(connection, id);\n                    if (p === undefined) {\n                        logger.log(`Received no promise from cancellation strategy when cancelling id ${id}`);\n                        return Promise.resolve();\n                    }\n                    else {\n                        return p.catch(() => {\n                            logger.log(`Sending cancellation messages for id ${id} failed`);\n                        });\n                    }\n                });\n            }\n            const requestMessage = {\n                jsonrpc: version,\n                id: id,\n                method: method,\n                params: messageParams\n            };\n            traceSendingRequest(requestMessage);\n            if (typeof cancellationStrategy.sender.enableCancellation === 'function') {\n                cancellationStrategy.sender.enableCancellation(requestMessage);\n            }\n            return new Promise(async (resolve, reject) => {\n                const resolveWithCleanup = (r) => {\n                    resolve(r);\n                    cancellationStrategy.sender.cleanup(id);\n                    disposable?.dispose();\n                };\n                const rejectWithCleanup = (r) => {\n                    reject(r);\n                    cancellationStrategy.sender.cleanup(id);\n                    disposable?.dispose();\n                };\n                const responsePromise = { method: method, timerStart: Date.now(), resolve: resolveWithCleanup, reject: rejectWithCleanup };\n                try {\n                    await messageWriter.write(requestMessage);\n                    responsePromises.set(id, responsePromise);\n                }\n                catch (error) {\n                    logger.error(`Sending request failed.`);\n                    // Writing the message failed. So we need to reject the promise.\n                    responsePromise.reject(new messages_1.ResponseError(messages_1.ErrorCodes.MessageWriteError, error.message ? error.message : 'Unknown reason'));\n                    throw error;\n                }\n            });\n        },\n        onRequest: (type, handler) => {\n            throwIfClosedOrDisposed();\n            let method = null;\n            if (StarRequestHandler.is(type)) {\n                method = undefined;\n                starRequestHandler = type;\n            }\n            else if (Is.string(type)) {\n                method = null;\n                if (handler !== undefined) {\n                    method = type;\n                    requestHandlers.set(type, { handler: handler, type: undefined });\n                }\n            }\n            else {\n                if (handler !== undefined) {\n                    method = type.method;\n                    requestHandlers.set(type.method, { type, handler });\n                }\n            }\n            return {\n                dispose: () => {\n                    if (method === null) {\n                        return;\n                    }\n                    if (method !== undefined) {\n                        requestHandlers.delete(method);\n                    }\n                    else {\n                        starRequestHandler = undefined;\n                    }\n                }\n            };\n        },\n        hasPendingResponse: () => {\n            return responsePromises.size > 0;\n        },\n        trace: async (_value, _tracer, sendNotificationOrTraceOptions) => {\n            let _sendNotification = false;\n            let _traceFormat = TraceFormat.Text;\n            if (sendNotificationOrTraceOptions !== undefined) {\n                if (Is.boolean(sendNotificationOrTraceOptions)) {\n                    _sendNotification = sendNotificationOrTraceOptions;\n                }\n                else {\n                    _sendNotification = sendNotificationOrTraceOptions.sendNotification || false;\n                    _traceFormat = sendNotificationOrTraceOptions.traceFormat || TraceFormat.Text;\n                }\n            }\n            trace = _value;\n            traceFormat = _traceFormat;\n            if (trace === Trace.Off) {\n                tracer = undefined;\n            }\n            else {\n                tracer = _tracer;\n            }\n            if (_sendNotification && !isClosed() && !isDisposed()) {\n                await connection.sendNotification(SetTraceNotification.type, { value: Trace.toString(_value) });\n            }\n        },\n        onError: errorEmitter.event,\n        onClose: closeEmitter.event,\n        onUnhandledNotification: unhandledNotificationEmitter.event,\n        onDispose: disposeEmitter.event,\n        end: () => {\n            messageWriter.end();\n        },\n        dispose: () => {\n            if (isDisposed()) {\n                return;\n            }\n            state = ConnectionState.Disposed;\n            disposeEmitter.fire(undefined);\n            const error = new messages_1.ResponseError(messages_1.ErrorCodes.PendingResponseRejected, 'Pending response rejected since connection got disposed');\n            for (const promise of responsePromises.values()) {\n                promise.reject(error);\n            }\n            responsePromises = new Map();\n            requestTokens = new Map();\n            knownCanceledRequests = new Set();\n            messageQueue = new linkedMap_1.LinkedMap();\n            // Test for backwards compatibility\n            if (Is.func(messageWriter.dispose)) {\n                messageWriter.dispose();\n            }\n            if (Is.func(messageReader.dispose)) {\n                messageReader.dispose();\n            }\n        },\n        listen: () => {\n            throwIfClosedOrDisposed();\n            throwIfListening();\n            state = ConnectionState.Listening;\n            messageReader.listen(callback);\n        },\n        inspect: () => {\n            // eslint-disable-next-line no-console\n            (0, ral_1.default)().console.log('inspect');\n        }\n    };\n    connection.onNotification(LogTraceNotification.type, (params) => {\n        if (trace === Trace.Off || !tracer) {\n            return;\n        }\n        const verbose = trace === Trace.Verbose || trace === Trace.Compact;\n        tracer.log(params.message, verbose ? params.verbose : undefined);\n    });\n    connection.onNotification(ProgressNotification.type, (params) => {\n        const handler = progressHandlers.get(params.token);\n        if (handler) {\n            handler(params.value);\n        }\n        else {\n            unhandledProgressEmitter.fire(params);\n        }\n    });\n    return connection;\n}\nexports.createMessageConnection = createMessageConnection;\n", "\"use strict\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Disposable = void 0;\nvar Disposable;\n(function (Disposable) {\n    function create(func) {\n        return {\n            dispose: func\n        };\n    }\n    Disposable.create = create;\n})(Disposable || (exports.Disposable = Disposable = {}));\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Emitter = exports.Event = void 0;\nconst ral_1 = require(\"./ral\");\nvar Event;\n(function (Event) {\n    const _disposable = { dispose() { } };\n    Event.None = function () { return _disposable; };\n})(Event || (exports.Event = Event = {}));\nclass CallbackList {\n    add(callback, context = null, bucket) {\n        if (!this._callbacks) {\n            this._callbacks = [];\n            this._contexts = [];\n        }\n        this._callbacks.push(callback);\n        this._contexts.push(context);\n        if (Array.isArray(bucket)) {\n            bucket.push({ dispose: () => this.remove(callback, context) });\n        }\n    }\n    remove(callback, context = null) {\n        if (!this._callbacks) {\n            return;\n        }\n        let foundCallbackWithDifferentContext = false;\n        for (let i = 0, len = this._callbacks.length; i < len; i++) {\n            if (this._callbacks[i] === callback) {\n                if (this._contexts[i] === context) {\n                    // callback & context match => remove it\n                    this._callbacks.splice(i, 1);\n                    this._contexts.splice(i, 1);\n                    return;\n                }\n                else {\n                    foundCallbackWithDifferentContext = true;\n                }\n            }\n        }\n        if (foundCallbackWithDifferentContext) {\n            throw new Error('When adding a listener with a context, you should remove it with the same context');\n        }\n    }\n    invoke(...args) {\n        if (!this._callbacks) {\n            return [];\n        }\n        const ret = [], callbacks = this._callbacks.slice(0), contexts = this._contexts.slice(0);\n        for (let i = 0, len = callbacks.length; i < len; i++) {\n            try {\n                ret.push(callbacks[i].apply(contexts[i], args));\n            }\n            catch (e) {\n                // eslint-disable-next-line no-console\n                (0, ral_1.default)().console.error(e);\n            }\n        }\n        return ret;\n    }\n    isEmpty() {\n        return !this._callbacks || this._callbacks.length === 0;\n    }\n    dispose() {\n        this._callbacks = undefined;\n        this._contexts = undefined;\n    }\n}\nclass Emitter {\n    constructor(_options) {\n        this._options = _options;\n    }\n    /**\n     * For the public to allow to subscribe\n     * to events from this Emitter\n     */\n    get event() {\n        if (!this._event) {\n            this._event = (listener, thisArgs, disposables) => {\n                if (!this._callbacks) {\n                    this._callbacks = new CallbackList();\n                }\n                if (this._options && this._options.onFirstListenerAdd && this._callbacks.isEmpty()) {\n                    this._options.onFirstListenerAdd(this);\n                }\n                this._callbacks.add(listener, thisArgs);\n                const result = {\n                    dispose: () => {\n                        if (!this._callbacks) {\n                            // disposable is disposed after emitter is disposed.\n                            return;\n                        }\n                        this._callbacks.remove(listener, thisArgs);\n                        result.dispose = Emitter._noop;\n                        if (this._options && this._options.onLastListenerRemove && this._callbacks.isEmpty()) {\n                            this._options.onLastListenerRemove(this);\n                        }\n                    }\n                };\n                if (Array.isArray(disposables)) {\n                    disposables.push(result);\n                }\n                return result;\n            };\n        }\n        return this._event;\n    }\n    /**\n     * To be kept private to fire an event to\n     * subscribers\n     */\n    fire(event) {\n        if (this._callbacks) {\n            this._callbacks.invoke.call(this._callbacks, event);\n        }\n    }\n    dispose() {\n        if (this._callbacks) {\n            this._callbacks.dispose();\n            this._callbacks = undefined;\n        }\n    }\n}\nexports.Emitter = Emitter;\nEmitter._noop = function () { };\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.stringArray = exports.array = exports.func = exports.error = exports.number = exports.string = exports.boolean = void 0;\nfunction boolean(value) {\n    return value === true || value === false;\n}\nexports.boolean = boolean;\nfunction string(value) {\n    return typeof value === 'string' || value instanceof String;\n}\nexports.string = string;\nfunction number(value) {\n    return typeof value === 'number' || value instanceof Number;\n}\nexports.number = number;\nfunction error(value) {\n    return value instanceof Error;\n}\nexports.error = error;\nfunction func(value) {\n    return typeof value === 'function';\n}\nexports.func = func;\nfunction array(value) {\n    return Array.isArray(value);\n}\nexports.array = array;\nfunction stringArray(value) {\n    return array(value) && value.every(elem => string(elem));\n}\nexports.stringArray = stringArray;\n", "\"use strict\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.LRUCache = exports.LinkedMap = exports.Touch = void 0;\nvar Touch;\n(function (Touch) {\n    Touch.None = 0;\n    Touch.First = 1;\n    Touch.AsOld = Touch.First;\n    Touch.Last = 2;\n    Touch.AsNew = Touch.Last;\n})(Touch || (exports.Touch = Touch = {}));\nclass LinkedMap {\n    constructor() {\n        this[_a] = 'LinkedMap';\n        this._map = new Map();\n        this._head = undefined;\n        this._tail = undefined;\n        this._size = 0;\n        this._state = 0;\n    }\n    clear() {\n        this._map.clear();\n        this._head = undefined;\n        this._tail = undefined;\n        this._size = 0;\n        this._state++;\n    }\n    isEmpty() {\n        return !this._head && !this._tail;\n    }\n    get size() {\n        return this._size;\n    }\n    get first() {\n        return this._head?.value;\n    }\n    get last() {\n        return this._tail?.value;\n    }\n    has(key) {\n        return this._map.has(key);\n    }\n    get(key, touch = Touch.None) {\n        const item = this._map.get(key);\n        if (!item) {\n            return undefined;\n        }\n        if (touch !== Touch.None) {\n            this.touch(item, touch);\n        }\n        return item.value;\n    }\n    set(key, value, touch = Touch.None) {\n        let item = this._map.get(key);\n        if (item) {\n            item.value = value;\n            if (touch !== Touch.None) {\n                this.touch(item, touch);\n            }\n        }\n        else {\n            item = { key, value, next: undefined, previous: undefined };\n            switch (touch) {\n                case Touch.None:\n                    this.addItemLast(item);\n                    break;\n                case Touch.First:\n                    this.addItemFirst(item);\n                    break;\n                case Touch.Last:\n                    this.addItemLast(item);\n                    break;\n                default:\n                    this.addItemLast(item);\n                    break;\n            }\n            this._map.set(key, item);\n            this._size++;\n        }\n        return this;\n    }\n    delete(key) {\n        return !!this.remove(key);\n    }\n    remove(key) {\n        const item = this._map.get(key);\n        if (!item) {\n            return undefined;\n        }\n        this._map.delete(key);\n        this.removeItem(item);\n        this._size--;\n        return item.value;\n    }\n    shift() {\n        if (!this._head && !this._tail) {\n            return undefined;\n        }\n        if (!this._head || !this._tail) {\n            throw new Error('Invalid list');\n        }\n        const item = this._head;\n        this._map.delete(item.key);\n        this.removeItem(item);\n        this._size--;\n        return item.value;\n    }\n    forEach(callbackfn, thisArg) {\n        const state = this._state;\n        let current = this._head;\n        while (current) {\n            if (thisArg) {\n                callbackfn.bind(thisArg)(current.value, current.key, this);\n            }\n            else {\n                callbackfn(current.value, current.key, this);\n            }\n            if (this._state !== state) {\n                throw new Error(`LinkedMap got modified during iteration.`);\n            }\n            current = current.next;\n        }\n    }\n    keys() {\n        const state = this._state;\n        let current = this._head;\n        const iterator = {\n            [Symbol.iterator]: () => {\n                return iterator;\n            },\n            next: () => {\n                if (this._state !== state) {\n                    throw new Error(`LinkedMap got modified during iteration.`);\n                }\n                if (current) {\n                    const result = { value: current.key, done: false };\n                    current = current.next;\n                    return result;\n                }\n                else {\n                    return { value: undefined, done: true };\n                }\n            }\n        };\n        return iterator;\n    }\n    values() {\n        const state = this._state;\n        let current = this._head;\n        const iterator = {\n            [Symbol.iterator]: () => {\n                return iterator;\n            },\n            next: () => {\n                if (this._state !== state) {\n                    throw new Error(`LinkedMap got modified during iteration.`);\n                }\n                if (current) {\n                    const result = { value: current.value, done: false };\n                    current = current.next;\n                    return result;\n                }\n                else {\n                    return { value: undefined, done: true };\n                }\n            }\n        };\n        return iterator;\n    }\n    entries() {\n        const state = this._state;\n        let current = this._head;\n        const iterator = {\n            [Symbol.iterator]: () => {\n                return iterator;\n            },\n            next: () => {\n                if (this._state !== state) {\n                    throw new Error(`LinkedMap got modified during iteration.`);\n                }\n                if (current) {\n                    const result = { value: [current.key, current.value], done: false };\n                    current = current.next;\n                    return result;\n                }\n                else {\n                    return { value: undefined, done: true };\n                }\n            }\n        };\n        return iterator;\n    }\n    [(_a = Symbol.toStringTag, Symbol.iterator)]() {\n        return this.entries();\n    }\n    trimOld(newSize) {\n        if (newSize >= this.size) {\n            return;\n        }\n        if (newSize === 0) {\n            this.clear();\n            return;\n        }\n        let current = this._head;\n        let currentSize = this.size;\n        while (current && currentSize > newSize) {\n            this._map.delete(current.key);\n            current = current.next;\n            currentSize--;\n        }\n        this._head = current;\n        this._size = currentSize;\n        if (current) {\n            current.previous = undefined;\n        }\n        this._state++;\n    }\n    addItemFirst(item) {\n        // First time Insert\n        if (!this._head && !this._tail) {\n            this._tail = item;\n        }\n        else if (!this._head) {\n            throw new Error('Invalid list');\n        }\n        else {\n            item.next = this._head;\n            this._head.previous = item;\n        }\n        this._head = item;\n        this._state++;\n    }\n    addItemLast(item) {\n        // First time Insert\n        if (!this._head && !this._tail) {\n            this._head = item;\n        }\n        else if (!this._tail) {\n            throw new Error('Invalid list');\n        }\n        else {\n            item.previous = this._tail;\n            this._tail.next = item;\n        }\n        this._tail = item;\n        this._state++;\n    }\n    removeItem(item) {\n        if (item === this._head && item === this._tail) {\n            this._head = undefined;\n            this._tail = undefined;\n        }\n        else if (item === this._head) {\n            // This can only happened if size === 1 which is handle\n            // by the case above.\n            if (!item.next) {\n                throw new Error('Invalid list');\n            }\n            item.next.previous = undefined;\n            this._head = item.next;\n        }\n        else if (item === this._tail) {\n            // This can only happened if size === 1 which is handle\n            // by the case above.\n            if (!item.previous) {\n                throw new Error('Invalid list');\n            }\n            item.previous.next = undefined;\n            this._tail = item.previous;\n        }\n        else {\n            const next = item.next;\n            const previous = item.previous;\n            if (!next || !previous) {\n                throw new Error('Invalid list');\n            }\n            next.previous = previous;\n            previous.next = next;\n        }\n        item.next = undefined;\n        item.previous = undefined;\n        this._state++;\n    }\n    touch(item, touch) {\n        if (!this._head || !this._tail) {\n            throw new Error('Invalid list');\n        }\n        if ((touch !== Touch.First && touch !== Touch.Last)) {\n            return;\n        }\n        if (touch === Touch.First) {\n            if (item === this._head) {\n                return;\n            }\n            const next = item.next;\n            const previous = item.previous;\n            // Unlink the item\n            if (item === this._tail) {\n                // previous must be defined since item was not head but is tail\n                // So there are more than on item in the map\n                previous.next = undefined;\n                this._tail = previous;\n            }\n            else {\n                // Both next and previous are not undefined since item was neither head nor tail.\n                next.previous = previous;\n                previous.next = next;\n            }\n            // Insert the node at head\n            item.previous = undefined;\n            item.next = this._head;\n            this._head.previous = item;\n            this._head = item;\n            this._state++;\n        }\n        else if (touch === Touch.Last) {\n            if (item === this._tail) {\n                return;\n            }\n            const next = item.next;\n            const previous = item.previous;\n            // Unlink the item.\n            if (item === this._head) {\n                // next must be defined since item was not tail but is head\n                // So there are more than on item in the map\n                next.previous = undefined;\n                this._head = next;\n            }\n            else {\n                // Both next and previous are not undefined since item was neither head nor tail.\n                next.previous = previous;\n                previous.next = next;\n            }\n            item.next = undefined;\n            item.previous = this._tail;\n            this._tail.next = item;\n            this._tail = item;\n            this._state++;\n        }\n    }\n    toJSON() {\n        const data = [];\n        this.forEach((value, key) => {\n            data.push([key, value]);\n        });\n        return data;\n    }\n    fromJSON(data) {\n        this.clear();\n        for (const [key, value] of data) {\n            this.set(key, value);\n        }\n    }\n}\nexports.LinkedMap = LinkedMap;\nclass LRUCache extends LinkedMap {\n    constructor(limit, ratio = 1) {\n        super();\n        this._limit = limit;\n        this._ratio = Math.min(Math.max(0, ratio), 1);\n    }\n    get limit() {\n        return this._limit;\n    }\n    set limit(limit) {\n        this._limit = limit;\n        this.checkTrim();\n    }\n    get ratio() {\n        return this._ratio;\n    }\n    set ratio(ratio) {\n        this._ratio = Math.min(Math.max(0, ratio), 1);\n        this.checkTrim();\n    }\n    get(key, touch = Touch.AsNew) {\n        return super.get(key, touch);\n    }\n    peek(key) {\n        return super.get(key, Touch.None);\n    }\n    set(key, value) {\n        super.set(key, value, Touch.Last);\n        this.checkTrim();\n        return this;\n    }\n    checkTrim() {\n        if (this.size > this._limit) {\n            this.trimOld(Math.round(this._limit * this._ratio));\n        }\n    }\n}\nexports.LRUCache = LRUCache;\n", "\"use strict\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractMessageBuffer = void 0;\nconst CR = 13;\nconst LF = 10;\nconst CRLF = '\\r\\n';\nclass AbstractMessageBuffer {\n    constructor(encoding = 'utf-8') {\n        this._encoding = encoding;\n        this._chunks = [];\n        this._totalLength = 0;\n    }\n    get encoding() {\n        return this._encoding;\n    }\n    append(chunk) {\n        const toAppend = typeof chunk === 'string' ? this.fromString(chunk, this._encoding) : chunk;\n        this._chunks.push(toAppend);\n        this._totalLength += toAppend.byteLength;\n    }\n    tryReadHeaders(lowerCaseKeys = false) {\n        if (this._chunks.length === 0) {\n            return undefined;\n        }\n        let state = 0;\n        let chunkIndex = 0;\n        let offset = 0;\n        let chunkBytesRead = 0;\n        row: while (chunkIndex < this._chunks.length) {\n            const chunk = this._chunks[chunkIndex];\n            offset = 0;\n            column: while (offset < chunk.length) {\n                const value = chunk[offset];\n                switch (value) {\n                    case CR:\n                        switch (state) {\n                            case 0:\n                                state = 1;\n                                break;\n                            case 2:\n                                state = 3;\n                                break;\n                            default:\n                                state = 0;\n                        }\n                        break;\n                    case LF:\n                        switch (state) {\n                            case 1:\n                                state = 2;\n                                break;\n                            case 3:\n                                state = 4;\n                                offset++;\n                                break row;\n                            default:\n                                state = 0;\n                        }\n                        break;\n                    default:\n                        state = 0;\n                }\n                offset++;\n            }\n            chunkBytesRead += chunk.byteLength;\n            chunkIndex++;\n        }\n        if (state !== 4) {\n            return undefined;\n        }\n        // The buffer contains the two CRLF at the end. So we will\n        // have two empty lines after the split at the end as well.\n        const buffer = this._read(chunkBytesRead + offset);\n        const result = new Map();\n        const headers = this.toString(buffer, 'ascii').split(CRLF);\n        if (headers.length < 2) {\n            return result;\n        }\n        for (let i = 0; i < headers.length - 2; i++) {\n            const header = headers[i];\n            const index = header.indexOf(':');\n            if (index === -1) {\n                throw new Error(`Message header must separate key and value using ':'\\n${header}`);\n            }\n            const key = header.substr(0, index);\n            const value = header.substr(index + 1).trim();\n            result.set(lowerCaseKeys ? key.toLowerCase() : key, value);\n        }\n        return result;\n    }\n    tryReadBody(length) {\n        if (this._totalLength < length) {\n            return undefined;\n        }\n        return this._read(length);\n    }\n    get numberOfBytes() {\n        return this._totalLength;\n    }\n    _read(byteCount) {\n        if (byteCount === 0) {\n            return this.emptyBuffer();\n        }\n        if (byteCount > this._totalLength) {\n            throw new Error(`Cannot read so many bytes!`);\n        }\n        if (this._chunks[0].byteLength === byteCount) {\n            // super fast path, precisely first chunk must be returned\n            const chunk = this._chunks[0];\n            this._chunks.shift();\n            this._totalLength -= byteCount;\n            return this.asNative(chunk);\n        }\n        if (this._chunks[0].byteLength > byteCount) {\n            // fast path, the reading is entirely within the first chunk\n            const chunk = this._chunks[0];\n            const result = this.asNative(chunk, byteCount);\n            this._chunks[0] = chunk.slice(byteCount);\n            this._totalLength -= byteCount;\n            return result;\n        }\n        const result = this.allocNative(byteCount);\n        let resultOffset = 0;\n        let chunkIndex = 0;\n        while (byteCount > 0) {\n            const chunk = this._chunks[chunkIndex];\n            if (chunk.byteLength > byteCount) {\n                // this chunk will survive\n                const chunkPart = chunk.slice(0, byteCount);\n                result.set(chunkPart, resultOffset);\n                resultOffset += byteCount;\n                this._chunks[chunkIndex] = chunk.slice(byteCount);\n                this._totalLength -= byteCount;\n                byteCount -= byteCount;\n            }\n            else {\n                // this chunk will be entirely read\n                result.set(chunk, resultOffset);\n                resultOffset += chunk.byteLength;\n                this._chunks.shift();\n                this._totalLength -= chunk.byteLength;\n                byteCount -= chunk.byteLength;\n            }\n        }\n        return result;\n    }\n}\nexports.AbstractMessageBuffer = AbstractMessageBuffer;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ReadableStreamMessageReader = exports.AbstractMessageReader = exports.MessageReader = void 0;\nconst ral_1 = require(\"./ral\");\nconst Is = require(\"./is\");\nconst events_1 = require(\"./events\");\nconst semaphore_1 = require(\"./semaphore\");\nvar MessageReader;\n(function (MessageReader) {\n    function is(value) {\n        let candidate = value;\n        return candidate && Is.func(candidate.listen) && Is.func(candidate.dispose) &&\n            Is.func(candidate.onError) && Is.func(candidate.onClose) && Is.func(candidate.onPartialMessage);\n    }\n    MessageReader.is = is;\n})(MessageReader || (exports.MessageReader = MessageReader = {}));\nclass AbstractMessageReader {\n    constructor() {\n        this.errorEmitter = new events_1.Emitter();\n        this.closeEmitter = new events_1.Emitter();\n        this.partialMessageEmitter = new events_1.Emitter();\n    }\n    dispose() {\n        this.errorEmitter.dispose();\n        this.closeEmitter.dispose();\n    }\n    get onError() {\n        return this.errorEmitter.event;\n    }\n    fireError(error) {\n        this.errorEmitter.fire(this.asError(error));\n    }\n    get onClose() {\n        return this.closeEmitter.event;\n    }\n    fireClose() {\n        this.closeEmitter.fire(undefined);\n    }\n    get onPartialMessage() {\n        return this.partialMessageEmitter.event;\n    }\n    firePartialMessage(info) {\n        this.partialMessageEmitter.fire(info);\n    }\n    asError(error) {\n        if (error instanceof Error) {\n            return error;\n        }\n        else {\n            return new Error(`Reader received error. Reason: ${Is.string(error.message) ? error.message : 'unknown'}`);\n        }\n    }\n}\nexports.AbstractMessageReader = AbstractMessageReader;\nvar ResolvedMessageReaderOptions;\n(function (ResolvedMessageReaderOptions) {\n    function fromOptions(options) {\n        let charset;\n        let result;\n        let contentDecoder;\n        const contentDecoders = new Map();\n        let contentTypeDecoder;\n        const contentTypeDecoders = new Map();\n        if (options === undefined || typeof options === 'string') {\n            charset = options ?? 'utf-8';\n        }\n        else {\n            charset = options.charset ?? 'utf-8';\n            if (options.contentDecoder !== undefined) {\n                contentDecoder = options.contentDecoder;\n                contentDecoders.set(contentDecoder.name, contentDecoder);\n            }\n            if (options.contentDecoders !== undefined) {\n                for (const decoder of options.contentDecoders) {\n                    contentDecoders.set(decoder.name, decoder);\n                }\n            }\n            if (options.contentTypeDecoder !== undefined) {\n                contentTypeDecoder = options.contentTypeDecoder;\n                contentTypeDecoders.set(contentTypeDecoder.name, contentTypeDecoder);\n            }\n            if (options.contentTypeDecoders !== undefined) {\n                for (const decoder of options.contentTypeDecoders) {\n                    contentTypeDecoders.set(decoder.name, decoder);\n                }\n            }\n        }\n        if (contentTypeDecoder === undefined) {\n            contentTypeDecoder = (0, ral_1.default)().applicationJson.decoder;\n            contentTypeDecoders.set(contentTypeDecoder.name, contentTypeDecoder);\n        }\n        return { charset, contentDecoder, contentDecoders, contentTypeDecoder, contentTypeDecoders };\n    }\n    ResolvedMessageReaderOptions.fromOptions = fromOptions;\n})(ResolvedMessageReaderOptions || (ResolvedMessageReaderOptions = {}));\nclass ReadableStreamMessageReader extends AbstractMessageReader {\n    constructor(readable, options) {\n        super();\n        this.readable = readable;\n        this.options = ResolvedMessageReaderOptions.fromOptions(options);\n        this.buffer = (0, ral_1.default)().messageBuffer.create(this.options.charset);\n        this._partialMessageTimeout = 10000;\n        this.nextMessageLength = -1;\n        this.messageToken = 0;\n        this.readSemaphore = new semaphore_1.Semaphore(1);\n    }\n    set partialMessageTimeout(timeout) {\n        this._partialMessageTimeout = timeout;\n    }\n    get partialMessageTimeout() {\n        return this._partialMessageTimeout;\n    }\n    listen(callback) {\n        this.nextMessageLength = -1;\n        this.messageToken = 0;\n        this.partialMessageTimer = undefined;\n        this.callback = callback;\n        const result = this.readable.onData((data) => {\n            this.onData(data);\n        });\n        this.readable.onError((error) => this.fireError(error));\n        this.readable.onClose(() => this.fireClose());\n        return result;\n    }\n    onData(data) {\n        try {\n            this.buffer.append(data);\n            while (true) {\n                if (this.nextMessageLength === -1) {\n                    const headers = this.buffer.tryReadHeaders(true);\n                    if (!headers) {\n                        return;\n                    }\n                    const contentLength = headers.get('content-length');\n                    if (!contentLength) {\n                        this.fireError(new Error(`Header must provide a Content-Length property.\\n${JSON.stringify(Object.fromEntries(headers))}`));\n                        return;\n                    }\n                    const length = parseInt(contentLength);\n                    if (isNaN(length)) {\n                        this.fireError(new Error(`Content-Length value must be a number. Got ${contentLength}`));\n                        return;\n                    }\n                    this.nextMessageLength = length;\n                }\n                const body = this.buffer.tryReadBody(this.nextMessageLength);\n                if (body === undefined) {\n                    /** We haven't received the full message yet. */\n                    this.setPartialMessageTimer();\n                    return;\n                }\n                this.clearPartialMessageTimer();\n                this.nextMessageLength = -1;\n                // Make sure that we convert one received message after the\n                // other. Otherwise it could happen that a decoding of a second\n                // smaller message finished before the decoding of a first larger\n                // message and then we would deliver the second message first.\n                this.readSemaphore.lock(async () => {\n                    const bytes = this.options.contentDecoder !== undefined\n                        ? await this.options.contentDecoder.decode(body)\n                        : body;\n                    const message = await this.options.contentTypeDecoder.decode(bytes, this.options);\n                    this.callback(message);\n                }).catch((error) => {\n                    this.fireError(error);\n                });\n            }\n        }\n        catch (error) {\n            this.fireError(error);\n        }\n    }\n    clearPartialMessageTimer() {\n        if (this.partialMessageTimer) {\n            this.partialMessageTimer.dispose();\n            this.partialMessageTimer = undefined;\n        }\n    }\n    setPartialMessageTimer() {\n        this.clearPartialMessageTimer();\n        if (this._partialMessageTimeout <= 0) {\n            return;\n        }\n        this.partialMessageTimer = (0, ral_1.default)().timer.setTimeout((token, timeout) => {\n            this.partialMessageTimer = undefined;\n            if (token === this.messageToken) {\n                this.firePartialMessage({ messageToken: token, waitingTime: timeout });\n                this.setPartialMessageTimer();\n            }\n        }, this._partialMessageTimeout, this.messageToken, this._partialMessageTimeout);\n    }\n}\nexports.ReadableStreamMessageReader = ReadableStreamMessageReader;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.WriteableStreamMessageWriter = exports.AbstractMessageWriter = exports.MessageWriter = void 0;\nconst ral_1 = require(\"./ral\");\nconst Is = require(\"./is\");\nconst semaphore_1 = require(\"./semaphore\");\nconst events_1 = require(\"./events\");\nconst ContentLength = 'Content-Length: ';\nconst CRLF = '\\r\\n';\nvar MessageWriter;\n(function (MessageWriter) {\n    function is(value) {\n        let candidate = value;\n        return candidate && Is.func(candidate.dispose) && Is.func(candidate.onClose) &&\n            Is.func(candidate.onError) && Is.func(candidate.write);\n    }\n    MessageWriter.is = is;\n})(MessageWriter || (exports.MessageWriter = MessageWriter = {}));\nclass AbstractMessageWriter {\n    constructor() {\n        this.errorEmitter = new events_1.Emitter();\n        this.closeEmitter = new events_1.Emitter();\n    }\n    dispose() {\n        this.errorEmitter.dispose();\n        this.closeEmitter.dispose();\n    }\n    get onError() {\n        return this.errorEmitter.event;\n    }\n    fireError(error, message, count) {\n        this.errorEmitter.fire([this.asError(error), message, count]);\n    }\n    get onClose() {\n        return this.closeEmitter.event;\n    }\n    fireClose() {\n        this.closeEmitter.fire(undefined);\n    }\n    asError(error) {\n        if (error instanceof Error) {\n            return error;\n        }\n        else {\n            return new Error(`Writer received error. Reason: ${Is.string(error.message) ? error.message : 'unknown'}`);\n        }\n    }\n}\nexports.AbstractMessageWriter = AbstractMessageWriter;\nvar ResolvedMessageWriterOptions;\n(function (ResolvedMessageWriterOptions) {\n    function fromOptions(options) {\n        if (options === undefined || typeof options === 'string') {\n            return { charset: options ?? 'utf-8', contentTypeEncoder: (0, ral_1.default)().applicationJson.encoder };\n        }\n        else {\n            return { charset: options.charset ?? 'utf-8', contentEncoder: options.contentEncoder, contentTypeEncoder: options.contentTypeEncoder ?? (0, ral_1.default)().applicationJson.encoder };\n        }\n    }\n    ResolvedMessageWriterOptions.fromOptions = fromOptions;\n})(ResolvedMessageWriterOptions || (ResolvedMessageWriterOptions = {}));\nclass WriteableStreamMessageWriter extends AbstractMessageWriter {\n    constructor(writable, options) {\n        super();\n        this.writable = writable;\n        this.options = ResolvedMessageWriterOptions.fromOptions(options);\n        this.errorCount = 0;\n        this.writeSemaphore = new semaphore_1.Semaphore(1);\n        this.writable.onError((error) => this.fireError(error));\n        this.writable.onClose(() => this.fireClose());\n    }\n    async write(msg) {\n        return this.writeSemaphore.lock(async () => {\n            const payload = this.options.contentTypeEncoder.encode(msg, this.options).then((buffer) => {\n                if (this.options.contentEncoder !== undefined) {\n                    return this.options.contentEncoder.encode(buffer);\n                }\n                else {\n                    return buffer;\n                }\n            });\n            return payload.then((buffer) => {\n                const headers = [];\n                headers.push(ContentLength, buffer.byteLength.toString(), CRLF);\n                headers.push(CRLF);\n                return this.doWrite(msg, headers, buffer);\n            }, (error) => {\n                this.fireError(error);\n                throw error;\n            });\n        });\n    }\n    async doWrite(msg, headers, data) {\n        try {\n            await this.writable.write(headers.join(''), 'ascii');\n            return this.writable.write(data);\n        }\n        catch (error) {\n            this.handleError(error, msg);\n            return Promise.reject(error);\n        }\n    }\n    handleError(error, msg) {\n        this.errorCount++;\n        this.fireError(error, msg, this.errorCount);\n    }\n    end() {\n        this.writable.end();\n    }\n}\nexports.WriteableStreamMessageWriter = WriteableStreamMessageWriter;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Message = exports.NotificationType9 = exports.NotificationType8 = exports.NotificationType7 = exports.NotificationType6 = exports.NotificationType5 = exports.NotificationType4 = exports.NotificationType3 = exports.NotificationType2 = exports.NotificationType1 = exports.NotificationType0 = exports.NotificationType = exports.RequestType9 = exports.RequestType8 = exports.RequestType7 = exports.RequestType6 = exports.RequestType5 = exports.RequestType4 = exports.RequestType3 = exports.RequestType2 = exports.RequestType1 = exports.RequestType = exports.RequestType0 = exports.AbstractMessageSignature = exports.ParameterStructures = exports.ResponseError = exports.ErrorCodes = void 0;\nconst is = require(\"./is\");\n/**\n * Predefined error codes.\n */\nvar ErrorCodes;\n(function (ErrorCodes) {\n    // Defined by JSON RPC\n    ErrorCodes.ParseError = -32700;\n    ErrorCodes.InvalidRequest = -32600;\n    ErrorCodes.MethodNotFound = -32601;\n    ErrorCodes.InvalidParams = -32602;\n    ErrorCodes.InternalError = -32603;\n    /**\n     * This is the start range of JSON RPC reserved error codes.\n     * It doesn't denote a real error code. No application error codes should\n     * be defined between the start and end range. For backwards\n     * compatibility the `ServerNotInitialized` and the `UnknownErrorCode`\n     * are left in the range.\n     *\n     * @since 3.16.0\n    */\n    ErrorCodes.jsonrpcReservedErrorRangeStart = -32099;\n    /** @deprecated use  jsonrpcReservedErrorRangeStart */\n    ErrorCodes.serverErrorStart = -32099;\n    /**\n     * An error occurred when write a message to the transport layer.\n     */\n    ErrorCodes.MessageWriteError = -32099;\n    /**\n     * An error occurred when reading a message from the transport layer.\n     */\n    ErrorCodes.MessageReadError = -32098;\n    /**\n     * The connection got disposed or lost and all pending responses got\n     * rejected.\n     */\n    ErrorCodes.PendingResponseRejected = -32097;\n    /**\n     * The connection is inactive and a use of it failed.\n     */\n    ErrorCodes.ConnectionInactive = -32096;\n    /**\n     * Error code indicating that a server received a notification or\n     * request before the server has received the `initialize` request.\n     */\n    ErrorCodes.ServerNotInitialized = -32002;\n    ErrorCodes.UnknownErrorCode = -32001;\n    /**\n     * This is the end range of JSON RPC reserved error codes.\n     * It doesn't denote a real error code.\n     *\n     * @since 3.16.0\n    */\n    ErrorCodes.jsonrpcReservedErrorRangeEnd = -32000;\n    /** @deprecated use  jsonrpcReservedErrorRangeEnd */\n    ErrorCodes.serverErrorEnd = -32000;\n})(ErrorCodes || (exports.ErrorCodes = ErrorCodes = {}));\n/**\n * An error object return in a response in case a request\n * has failed.\n */\nclass ResponseError extends Error {\n    constructor(code, message, data) {\n        super(message);\n        this.code = is.number(code) ? code : ErrorCodes.UnknownErrorCode;\n        this.data = data;\n        Object.setPrototypeOf(this, ResponseError.prototype);\n    }\n    toJson() {\n        const result = {\n            code: this.code,\n            message: this.message\n        };\n        if (this.data !== undefined) {\n            result.data = this.data;\n        }\n        return result;\n    }\n}\nexports.ResponseError = ResponseError;\nclass ParameterStructures {\n    constructor(kind) {\n        this.kind = kind;\n    }\n    static is(value) {\n        return value === ParameterStructures.auto || value === ParameterStructures.byName || value === ParameterStructures.byPosition;\n    }\n    toString() {\n        return this.kind;\n    }\n}\nexports.ParameterStructures = ParameterStructures;\n/**\n * The parameter structure is automatically inferred on the number of parameters\n * and the parameter type in case of a single param.\n */\nParameterStructures.auto = new ParameterStructures('auto');\n/**\n * Forces `byPosition` parameter structure. This is useful if you have a single\n * parameter which has a literal type.\n */\nParameterStructures.byPosition = new ParameterStructures('byPosition');\n/**\n * Forces `byName` parameter structure. This is only useful when having a single\n * parameter. The library will report errors if used with a different number of\n * parameters.\n */\nParameterStructures.byName = new ParameterStructures('byName');\n/**\n * An abstract implementation of a MessageType.\n */\nclass AbstractMessageSignature {\n    constructor(method, numberOfParams) {\n        this.method = method;\n        this.numberOfParams = numberOfParams;\n    }\n    get parameterStructures() {\n        return ParameterStructures.auto;\n    }\n}\nexports.AbstractMessageSignature = AbstractMessageSignature;\n/**\n * Classes to type request response pairs\n */\nclass RequestType0 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 0);\n    }\n}\nexports.RequestType0 = RequestType0;\nclass RequestType extends AbstractMessageSignature {\n    constructor(method, _parameterStructures = ParameterStructures.auto) {\n        super(method, 1);\n        this._parameterStructures = _parameterStructures;\n    }\n    get parameterStructures() {\n        return this._parameterStructures;\n    }\n}\nexports.RequestType = RequestType;\nclass RequestType1 extends AbstractMessageSignature {\n    constructor(method, _parameterStructures = ParameterStructures.auto) {\n        super(method, 1);\n        this._parameterStructures = _parameterStructures;\n    }\n    get parameterStructures() {\n        return this._parameterStructures;\n    }\n}\nexports.RequestType1 = RequestType1;\nclass RequestType2 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 2);\n    }\n}\nexports.RequestType2 = RequestType2;\nclass RequestType3 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 3);\n    }\n}\nexports.RequestType3 = RequestType3;\nclass RequestType4 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 4);\n    }\n}\nexports.RequestType4 = RequestType4;\nclass RequestType5 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 5);\n    }\n}\nexports.RequestType5 = RequestType5;\nclass RequestType6 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 6);\n    }\n}\nexports.RequestType6 = RequestType6;\nclass RequestType7 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 7);\n    }\n}\nexports.RequestType7 = RequestType7;\nclass RequestType8 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 8);\n    }\n}\nexports.RequestType8 = RequestType8;\nclass RequestType9 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 9);\n    }\n}\nexports.RequestType9 = RequestType9;\nclass NotificationType extends AbstractMessageSignature {\n    constructor(method, _parameterStructures = ParameterStructures.auto) {\n        super(method, 1);\n        this._parameterStructures = _parameterStructures;\n    }\n    get parameterStructures() {\n        return this._parameterStructures;\n    }\n}\nexports.NotificationType = NotificationType;\nclass NotificationType0 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 0);\n    }\n}\nexports.NotificationType0 = NotificationType0;\nclass NotificationType1 extends AbstractMessageSignature {\n    constructor(method, _parameterStructures = ParameterStructures.auto) {\n        super(method, 1);\n        this._parameterStructures = _parameterStructures;\n    }\n    get parameterStructures() {\n        return this._parameterStructures;\n    }\n}\nexports.NotificationType1 = NotificationType1;\nclass NotificationType2 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 2);\n    }\n}\nexports.NotificationType2 = NotificationType2;\nclass NotificationType3 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 3);\n    }\n}\nexports.NotificationType3 = NotificationType3;\nclass NotificationType4 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 4);\n    }\n}\nexports.NotificationType4 = NotificationType4;\nclass NotificationType5 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 5);\n    }\n}\nexports.NotificationType5 = NotificationType5;\nclass NotificationType6 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 6);\n    }\n}\nexports.NotificationType6 = NotificationType6;\nclass NotificationType7 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 7);\n    }\n}\nexports.NotificationType7 = NotificationType7;\nclass NotificationType8 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 8);\n    }\n}\nexports.NotificationType8 = NotificationType8;\nclass NotificationType9 extends AbstractMessageSignature {\n    constructor(method) {\n        super(method, 9);\n    }\n}\nexports.NotificationType9 = NotificationType9;\nvar Message;\n(function (Message) {\n    /**\n     * Tests if the given message is a request message\n     */\n    function isRequest(message) {\n        const candidate = message;\n        return candidate && is.string(candidate.method) && (is.string(candidate.id) || is.number(candidate.id));\n    }\n    Message.isRequest = isRequest;\n    /**\n     * Tests if the given message is a notification message\n     */\n    function isNotification(message) {\n        const candidate = message;\n        return candidate && is.string(candidate.method) && message.id === void 0;\n    }\n    Message.isNotification = isNotification;\n    /**\n     * Tests if the given message is a response message\n     */\n    function isResponse(message) {\n        const candidate = message;\n        return candidate && (candidate.result !== void 0 || !!candidate.error) && (is.string(candidate.id) || is.number(candidate.id) || candidate.id === null);\n    }\n    Message.isResponse = isResponse;\n})(Message || (exports.Message = Message = {}));\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nlet _ral;\nfunction RAL() {\n    if (_ral === undefined) {\n        throw new Error(`No runtime abstraction layer installed`);\n    }\n    return _ral;\n}\n(function (RAL) {\n    function install(ral) {\n        if (ral === undefined) {\n            throw new Error(`No runtime abstraction layer provided`);\n        }\n        _ral = ral;\n    }\n    RAL.install = install;\n})(RAL || (RAL = {}));\nexports.default = RAL;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Semaphore = void 0;\nconst ral_1 = require(\"./ral\");\nclass Semaphore {\n    constructor(capacity = 1) {\n        if (capacity <= 0) {\n            throw new Error('Capacity must be greater than 0');\n        }\n        this._capacity = capacity;\n        this._active = 0;\n        this._waiting = [];\n    }\n    lock(thunk) {\n        return new Promise((resolve, reject) => {\n            this._waiting.push({ thunk, resolve, reject });\n            this.runNext();\n        });\n    }\n    get active() {\n        return this._active;\n    }\n    runNext() {\n        if (this._waiting.length === 0 || this._active === this._capacity) {\n            return;\n        }\n        (0, ral_1.default)().timer.setImmediate(() => this.doRunNext());\n    }\n    doRunNext() {\n        if (this._waiting.length === 0 || this._active === this._capacity) {\n            return;\n        }\n        const next = this._waiting.shift();\n        this._active++;\n        if (this._active > this._capacity) {\n            throw new Error(`To many thunks active`);\n        }\n        try {\n            const result = next.thunk();\n            if (result instanceof Promise) {\n                result.then((value) => {\n                    this._active--;\n                    next.resolve(value);\n                    this.runNext();\n                }, (err) => {\n                    this._active--;\n                    next.reject(err);\n                    this.runNext();\n                });\n            }\n            else {\n                this._active--;\n                next.resolve(result);\n                this.runNext();\n            }\n        }\n        catch (err) {\n            this._active--;\n            next.reject(err);\n            this.runNext();\n        }\n    }\n}\nexports.Semaphore = Semaphore;\n", "\"use strict\";\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SharedArrayReceiverStrategy = exports.SharedArraySenderStrategy = void 0;\nconst cancellation_1 = require(\"./cancellation\");\nvar CancellationState;\n(function (CancellationState) {\n    CancellationState.Continue = 0;\n    CancellationState.Cancelled = 1;\n})(CancellationState || (CancellationState = {}));\nclass SharedArraySenderStrategy {\n    constructor() {\n        this.buffers = new Map();\n    }\n    enableCancellation(request) {\n        if (request.id === null) {\n            return;\n        }\n        const buffer = new SharedArrayBuffer(4);\n        const data = new Int32Array(buffer, 0, 1);\n        data[0] = CancellationState.Continue;\n        this.buffers.set(request.id, buffer);\n        request.$cancellationData = buffer;\n    }\n    async sendCancellation(_conn, id) {\n        const buffer = this.buffers.get(id);\n        if (buffer === undefined) {\n            return;\n        }\n        const data = new Int32Array(buffer, 0, 1);\n        Atomics.store(data, 0, CancellationState.Cancelled);\n    }\n    cleanup(id) {\n        this.buffers.delete(id);\n    }\n    dispose() {\n        this.buffers.clear();\n    }\n}\nexports.SharedArraySenderStrategy = SharedArraySenderStrategy;\nclass SharedArrayBufferCancellationToken {\n    constructor(buffer) {\n        this.data = new Int32Array(buffer, 0, 1);\n    }\n    get isCancellationRequested() {\n        return Atomics.load(this.data, 0) === CancellationState.Cancelled;\n    }\n    get onCancellationRequested() {\n        throw new Error(`Cancellation over SharedArrayBuffer doesn't support cancellation events`);\n    }\n}\nclass SharedArrayBufferCancellationTokenSource {\n    constructor(buffer) {\n        this.token = new SharedArrayBufferCancellationToken(buffer);\n    }\n    cancel() {\n    }\n    dispose() {\n    }\n}\nclass SharedArrayReceiverStrategy {\n    constructor() {\n        this.kind = 'request';\n    }\n    createCancellationTokenSource(request) {\n        const buffer = request.$cancellationData;\n        if (buffer === undefined) {\n            return new cancellation_1.CancellationTokenSource();\n        }\n        return new SharedArrayBufferCancellationTokenSource(buffer);\n    }\n}\nexports.SharedArrayReceiverStrategy = SharedArrayReceiverStrategy;\n", "/* --------------------------------------------------------------------------------------------\r\n * Copyright (c) 2018-2022 TypeFox GmbH (http://www.typefox.io). All rights reserved.\r\n * Licensed under the MIT License. See License.txt in the project root for license information.\r\n * ------------------------------------------------------------------------------------------ */\r\nimport { Disposable } from 'vscode-jsonrpc';\r\nexport class DisposableCollection {\r\n    constructor() {\r\n        this.disposables = [];\r\n    }\r\n    dispose() {\r\n        while (this.disposables.length !== 0) {\r\n            this.disposables.pop().dispose();\r\n        }\r\n    }\r\n    push(disposable) {\r\n        const disposables = this.disposables;\r\n        disposables.push(disposable);\r\n        return {\r\n            dispose() {\r\n                const index = disposables.indexOf(disposable);\r\n                if (index !== -1) {\r\n                    disposables.splice(index, 1);\r\n                }\r\n            }\r\n        };\r\n    }\r\n}\r\nexport { Disposable };\r\n//# sourceMappingURL=disposable.js.map", "/* --------------------------------------------------------------------------------------------\r\n * Copyright (c) 2018-2022 TypeFox GmbH (http://www.typefox.io). All rights reserved.\r\n * Licensed under the MIT License. See License.txt in the project root for license information.\r\n * ------------------------------------------------------------------------------------------ */\r\nimport { AbstractMessageReader } from 'vscode-jsonrpc/lib/common/messageReader';\r\nexport class WebSocketMessageReader extends AbstractMessageReader {\r\n    constructor(socket) {\r\n        super();\r\n        this.socket = socket;\r\n        this.state = 'initial';\r\n        this.events = [];\r\n        this.socket.onMessage(message => this.readMessage(message));\r\n        this.socket.onError(error => this.fireError(error));\r\n        this.socket.onClose((code, reason) => {\r\n            if (code !== 1000) {\r\n                const error = {\r\n                    name: '' + code,\r\n                    message: `Error during socket reconnect: code = ${code}, reason = ${reason}`\r\n                };\r\n                this.fireError(error);\r\n            }\r\n            this.fireClose();\r\n        });\r\n    }\r\n    listen(callback) {\r\n        if (this.state === 'initial') {\r\n            this.state = 'listening';\r\n            this.callback = callback;\r\n            while (this.events.length !== 0) {\r\n                const event = this.events.pop();\r\n                if (event.message) {\r\n                    this.readMessage(event.message);\r\n                }\r\n                else if (event.error) {\r\n                    this.fireError(event.error);\r\n                }\r\n                else {\r\n                    this.fireClose();\r\n                }\r\n            }\r\n        }\r\n        return {\r\n            dispose: () => {\r\n                if (this.callback === callback) {\r\n                    this.callback = undefined;\r\n                }\r\n            }\r\n        };\r\n    }\r\n    readMessage(message) {\r\n        if (this.state === 'initial') {\r\n            this.events.splice(0, 0, { message });\r\n        }\r\n        else if (this.state === 'listening') {\r\n            const data = JSON.parse(message);\r\n            this.callback(data);\r\n        }\r\n    }\r\n    fireError(error) {\r\n        if (this.state === 'initial') {\r\n            this.events.splice(0, 0, { error });\r\n        }\r\n        else if (this.state === 'listening') {\r\n            super.fireError(error);\r\n        }\r\n    }\r\n    fireClose() {\r\n        if (this.state === 'initial') {\r\n            this.events.splice(0, 0, {});\r\n        }\r\n        else if (this.state === 'listening') {\r\n            super.fireClose();\r\n        }\r\n        this.state = 'closed';\r\n    }\r\n}\r\n//# sourceMappingURL=reader.js.map", "/* --------------------------------------------------------------------------------------------\r\n * Copyright (c) 2018-2022 TypeFox GmbH (http://www.typefox.io). All rights reserved.\r\n * Licensed under the MIT License. See License.txt in the project root for license information.\r\n * ------------------------------------------------------------------------------------------ */\r\nimport { AbstractMessageWriter } from 'vscode-jsonrpc/lib/common/messageWriter';\r\nexport class WebSocketMessageWriter extends AbstractMessageWriter {\r\n    constructor(socket) {\r\n        super();\r\n        this.socket = socket;\r\n        this.errorCount = 0;\r\n    }\r\n    end() {\r\n    }\r\n    async write(msg) {\r\n        try {\r\n            const content = JSON.stringify(msg);\r\n            this.socket.send(content);\r\n        }\r\n        catch (e) {\r\n            this.errorCount++;\r\n            this.fireError(e, msg, this.errorCount);\r\n        }\r\n    }\r\n}\r\n//# sourceMappingURL=writer.js.map", "/* --------------------------------------------------------------------------------------------\r\n * Copyright (c) 2018-2022 TypeFox GmbH (http://www.typefox.io). All rights reserved.\r\n * Licensed under the MIT License. See License.txt in the project root for license information.\r\n * ------------------------------------------------------------------------------------------ */\r\nimport { createMessageConnection } from 'vscode-jsonrpc';\r\nimport { WebSocketMessageReader } from './reader';\r\nimport { WebSocketMessageWriter } from './writer';\r\nexport function createWebSocketConnection(socket, logger) {\r\n    const messageReader = new WebSocketMessageReader(socket);\r\n    const messageWriter = new WebSocketMessageWriter(socket);\r\n    const connection = createMessageConnection(messageReader, messageWriter, logger);\r\n    connection.onClose(() => connection.dispose());\r\n    return connection;\r\n}\r\n//# sourceMappingURL=connection.js.map", "/* --------------------------------------------------------------------------------------------\r\n * Copyright (c) 2018-2022 TypeFox GmbH (http://www.typefox.io). All rights reserved.\r\n * Licensed under the MIT License. See License.txt in the project root for license information.\r\n * ------------------------------------------------------------------------------------------ */\r\nexport * from './socket';\r\nexport * from './reader';\r\nexport * from './writer';\r\nexport * from './connection';\r\n//# sourceMappingURL=index.js.map", "export class ConsoleLogger {\r\n    error(message) {\r\n        console.error(message);\r\n    }\r\n    warn(message) {\r\n        console.warn(message);\r\n    }\r\n    info(message) {\r\n        console.info(message);\r\n    }\r\n    log(message) {\r\n        console.log(message);\r\n    }\r\n    debug(message) {\r\n        console.debug(message);\r\n    }\r\n}\r\n//# sourceMappingURL=logger.js.map", "/* --------------------------------------------------------------------------------------------\r\n * Copyright (c) 2018-2022 TypeFox GmbH (http://www.typefox.io). All rights reserved.\r\n * Licensed under the MIT License. See License.txt in the project root for license information.\r\n * ------------------------------------------------------------------------------------------ */\r\nimport { createWebSocketConnection } from './socket';\r\n/* --------------------------------------------------------------------------------------------\r\n * Copyright (c) 2018-2022 TypeFox GmbH (http://www.typefox.io). All rights reserved.\r\n * Licensed under the MIT License. See License.txt in the project root for license information.\r\n * ------------------------------------------------------------------------------------------ */\r\nimport { ConsoleLogger } from './logger';\r\nexport function listen(options) {\r\n    const { webSocket, onConnection } = options;\r\n    const logger = options.logger || new ConsoleLogger();\r\n    webSocket.onopen = () => {\r\n        const socket = toSocket(webSocket);\r\n        const connection = createWebSocketConnection(socket, logger);\r\n        onConnection(connection);\r\n    };\r\n}\r\nexport function toSocket(webSocket) {\r\n    return {\r\n        send: content => webSocket.send(content),\r\n        onMessage: cb => {\r\n            webSocket.onmessage = event => cb(event.data);\r\n        },\r\n        onError: cb => {\r\n            webSocket.onerror = event => {\r\n                if ('message' in event) {\r\n                    cb(event.message);\r\n                }\r\n            };\r\n        },\r\n        onClose: cb => {\r\n            webSocket.onclose = event => cb(event.code, event.reason);\r\n        },\r\n        dispose: () => webSocket.close()\r\n    };\r\n}\r\n//# sourceMappingURL=connection.js.map", "/* --------------------------------------------------------------------------------------------\r\n * Copyright (c) 2018-2022 TypeFox GmbH (http://www.typefox.io). All rights reserved.\r\n * Licensed under the MIT License. See License.txt in the project root for license information.\r\n * ------------------------------------------------------------------------------------------ */\r\nexport * from 'vscode-jsonrpc';\r\nexport * from 'vscode-jsonrpc/lib/common/messages';\r\nexport * from './disposable';\r\nexport * from './socket';\r\nexport * from './logger';\r\nexport * from './connection';\r\n//# sourceMappingURL=index.js.map"], "names": [], "sourceRoot": ""}