{"version": 3, "file": "9558.255ac6fa674e07653e39.js?v=255ac6fa674e07653e39", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,yBAAyB;AACzB;AACA,MAAM;AACN,2BAA2B;AAC3B;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,yDAAyD,sDAAsD;AACrH;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,IAAI,sBAAsB;AAC1B;AACA;AACA,2BAA2B;AAC3B;AACA,QAAQ;AACR,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,MAAM,4BAA4B;AAClC;AACA;AACA;AACA;AACA,iBAAiB;AACjB,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,YAAY;AACZ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,oBAAoB,QAAQ,QAAQ,cAAc;AAClD;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/jinja2.js"], "sourcesContent": ["var keywords = [\"and\", \"as\", \"block\", \"endblock\", \"by\", \"cycle\", \"debug\", \"else\", \"elif\",\n                \"extends\", \"filter\", \"endfilter\", \"firstof\", \"do\", \"for\",\n                \"endfor\", \"if\", \"endif\", \"ifchanged\", \"endifchanged\",\n                \"ifequal\", \"endifequal\", \"ifnotequal\", \"set\", \"raw\", \"endraw\",\n                \"endifnotequal\", \"in\", \"include\", \"load\", \"not\", \"now\", \"or\",\n                \"parsed\", \"regroup\", \"reversed\", \"spaceless\", \"call\", \"endcall\", \"macro\",\n                \"endmacro\", \"endspaceless\", \"ssi\", \"templatetag\", \"openblock\",\n                \"closeblock\", \"openvariable\", \"closevariable\", \"without\", \"context\",\n                \"openbrace\", \"closebrace\", \"opencomment\",\n                \"closecomment\", \"widthratio\", \"url\", \"with\", \"endwith\",\n                \"get_current_language\", \"trans\", \"endtrans\", \"noop\", \"blocktrans\",\n                \"endblocktrans\", \"get_available_languages\",\n                \"get_current_language_bidi\", \"pluralize\", \"autoescape\", \"endautoescape\"],\n    operator = /^[+\\-*&%=<>!?|~^]/,\n    sign = /^[:\\[\\(\\{]/,\n    atom = [\"true\", \"false\"],\n    number = /^(\\d[+\\-\\*\\/])?\\d+(\\.\\d+)?/;\n\nkeywords = new RegExp(\"((\" + keywords.join(\")|(\") + \"))\\\\b\");\natom = new RegExp(\"((\" + atom.join(\")|(\") + \"))\\\\b\");\n\nfunction tokenBase (stream, state) {\n  var ch = stream.peek();\n\n  //Comment\n  if (state.incomment) {\n    if(!stream.skipTo(\"#}\")) {\n      stream.skipToEnd();\n    } else {\n      stream.eatWhile(/\\#|}/);\n      state.incomment = false;\n    }\n    return \"comment\";\n    //Tag\n  } else if (state.intag) {\n    //After operator\n    if(state.operator) {\n      state.operator = false;\n      if(stream.match(atom)) {\n        return \"atom\";\n      }\n      if(stream.match(number)) {\n        return \"number\";\n      }\n    }\n    //After sign\n    if(state.sign) {\n      state.sign = false;\n      if(stream.match(atom)) {\n        return \"atom\";\n      }\n      if(stream.match(number)) {\n        return \"number\";\n      }\n    }\n\n    if(state.instring) {\n      if(ch == state.instring) {\n        state.instring = false;\n      }\n      stream.next();\n      return \"string\";\n    } else if(ch == \"'\" || ch == '\"') {\n      state.instring = ch;\n      stream.next();\n      return \"string\";\n    } else if (state.inbraces > 0 && ch ==\")\") {\n      stream.next()\n      state.inbraces--;\n    }\n    else if (ch == \"(\") {\n      stream.next()\n      state.inbraces++;\n    }\n    else if (state.inbrackets > 0 && ch ==\"]\") {\n      stream.next()\n      state.inbrackets--;\n    }\n    else if (ch == \"[\") {\n      stream.next()\n      state.inbrackets++;\n    } else if (!state.lineTag && (stream.match(state.intag + \"}\") || stream.eat(\"-\") && stream.match(state.intag + \"}\"))) {\n      state.intag = false;\n      return \"tag\";\n    } else if(stream.match(operator)) {\n      state.operator = true;\n      return \"operator\";\n    } else if(stream.match(sign)) {\n      state.sign = true;\n    } else {\n      if (stream.column() == 1 && state.lineTag && stream.match(keywords)) {\n        //allow nospace after tag before the keyword\n        return \"keyword\";\n      }\n      if(stream.eat(\" \") || stream.sol()) {\n        if(stream.match(keywords)) {\n          return \"keyword\";\n        }\n        if(stream.match(atom)) {\n          return \"atom\";\n        }\n        if(stream.match(number)) {\n          return \"number\";\n        }\n        if(stream.sol()) {\n          stream.next();\n        }\n      } else {\n        stream.next();\n      }\n\n    }\n    return \"variable\";\n  } else if (stream.eat(\"{\")) {\n    if (stream.eat(\"#\")) {\n      state.incomment = true;\n      if(!stream.skipTo(\"#}\")) {\n        stream.skipToEnd();\n      } else {\n        stream.eatWhile(/\\#|}/);\n        state.incomment = false;\n      }\n      return \"comment\";\n      //Open tag\n    } else if (ch = stream.eat(/\\{|%/)) {\n      //Cache close tag\n      state.intag = ch;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      if(ch == \"{\") {\n        state.intag = \"}\";\n      }\n      stream.eat(\"-\");\n      return \"tag\";\n    }\n    //Line statements\n  } else if (stream.eat('#')) {\n    if (stream.peek() == '#') {\n      stream.skipToEnd();\n      return \"comment\"\n    }\n    else if (!stream.eol()) {\n      state.intag = true;\n      state.lineTag = true;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      return \"tag\";\n    }\n  }\n  stream.next();\n};\n\nexport const jinja2 = {\n  name: \"jinja2\",\n  startState: function () {\n    return {tokenize: tokenBase, inbrackets: 0, inbraces: 0};\n  },\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    if (stream.eol() && state.lineTag && !state.instring && state.inbraces == 0 && state.inbrackets == 0) {\n      //Close line statement at the EOL\n      state.intag = false\n      state.lineTag = false\n    }\n    return style;\n  },\n  languageData: {\n    commentTokens: {block: {open: \"{#\", close: \"#}\", line: \"##\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}