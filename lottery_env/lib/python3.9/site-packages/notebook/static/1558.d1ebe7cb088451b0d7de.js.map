{"version": 3, "file": "1558.d1ebe7cb088451b0d7de.js?v=d1ebe7cb088451b0d7de", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,0BAA0B;AAC1B,0BAA0B;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA,6BAA6B,SAAS,gBAAgB;AACtD,6BAA6B,SAAS,WAAW;;AAEjD;AACA,6CAA6C,cAAc;AAC3D;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/elm.js"], "sourcesContent": ["function switchState(source, setState, f)\n{\n  setState(f);\n  return f(source, setState);\n}\n\nvar lowerRE = /[a-z]/;\nvar upperRE = /[A-Z]/;\nvar innerRE = /[a-zA-Z0-9_]/;\n\nvar digitRE = /[0-9]/;\nvar hexRE = /[0-9A-Fa-f]/;\nvar symbolRE = /[-&*+.\\\\/<>=?^|:]/;\nvar specialRE = /[(),[\\]{}]/;\nvar spacesRE = /[ \\v\\f]/; // newlines are handled in tokenizer\n\nfunction normal()\n{\n  return function(source, setState)\n  {\n    if (source.eatWhile(spacesRE))\n    {\n      return null;\n    }\n\n    var char = source.next();\n\n    if (specialRE.test(char))\n    {\n      return (char === '{' && source.eat('-'))\n        ? switchState(source, setState, chompMultiComment(1))\n        : (char === '[' && source.match('glsl|'))\n        ? switchState(source, setState, chompGlsl)\n        : 'builtin';\n    }\n\n    if (char === '\\'')\n    {\n      return switchState(source, setState, chompChar);\n    }\n\n    if (char === '\"')\n    {\n      return source.eat('\"')\n        ? source.eat('\"')\n        ? switchState(source, setState, chompMultiString)\n        : 'string'\n      : switchState(source, setState, chompSingleString);\n    }\n\n    if (upperRE.test(char))\n    {\n      source.eatWhile(innerRE);\n      return 'type';\n    }\n\n    if (lowerRE.test(char))\n    {\n      var isDef = source.pos === 1;\n      source.eatWhile(innerRE);\n      return isDef ? \"def\" : \"variable\";\n    }\n\n    if (digitRE.test(char))\n    {\n      if (char === '0')\n      {\n        if (source.eat(/[xX]/))\n        {\n          source.eatWhile(hexRE); // should require at least 1\n          return \"number\";\n        }\n      }\n      else\n      {\n        source.eatWhile(digitRE);\n      }\n      if (source.eat('.'))\n      {\n        source.eatWhile(digitRE); // should require at least 1\n      }\n      if (source.eat(/[eE]/))\n      {\n        source.eat(/[-+]/);\n        source.eatWhile(digitRE); // should require at least 1\n      }\n      return \"number\";\n    }\n\n    if (symbolRE.test(char))\n    {\n      if (char === '-' && source.eat('-'))\n      {\n        source.skipToEnd();\n        return \"comment\";\n      }\n      source.eatWhile(symbolRE);\n      return \"keyword\";\n    }\n\n    if (char === '_')\n    {\n      return \"keyword\";\n    }\n\n    return \"error\";\n  }\n}\n\nfunction chompMultiComment(nest)\n{\n  if (nest == 0)\n  {\n    return normal();\n  }\n  return function(source, setState)\n  {\n    while (!source.eol())\n    {\n      var char = source.next();\n      if (char == '{' && source.eat('-'))\n      {\n        ++nest;\n      }\n      else if (char == '-' && source.eat('}'))\n      {\n        --nest;\n        if (nest === 0)\n        {\n          setState(normal());\n          return 'comment';\n        }\n      }\n    }\n    setState(chompMultiComment(nest));\n    return 'comment';\n  }\n}\n\nfunction chompMultiString(source, setState)\n{\n  while (!source.eol())\n  {\n    var char = source.next();\n    if (char === '\"' && source.eat('\"') && source.eat('\"'))\n    {\n      setState(normal());\n      return 'string';\n    }\n  }\n  return 'string';\n}\n\nfunction chompSingleString(source, setState)\n{\n  while (source.skipTo('\\\\\"')) { source.next(); source.next(); }\n  if (source.skipTo('\"'))\n  {\n    source.next();\n    setState(normal());\n    return 'string';\n  }\n  source.skipToEnd();\n  setState(normal());\n  return 'error';\n}\n\nfunction chompChar(source, setState)\n{\n  while (source.skipTo(\"\\\\'\")) { source.next(); source.next(); }\n  if (source.skipTo(\"'\"))\n  {\n    source.next();\n    setState(normal());\n    return 'string';\n  }\n  source.skipToEnd();\n  setState(normal());\n  return 'error';\n}\n\nfunction chompGlsl(source, setState)\n{\n  while (!source.eol())\n  {\n    var char = source.next();\n    if (char === '|' && source.eat(']'))\n    {\n      setState(normal());\n      return 'string';\n    }\n  }\n  return 'string';\n}\n\nvar wellKnownWords = {\n  case: 1,\n  of: 1,\n  as: 1,\n  if: 1,\n  then: 1,\n  else: 1,\n  let: 1,\n    in: 1,\n  type: 1,\n  alias: 1,\n  module: 1,\n  where: 1,\n  import: 1,\n  exposing: 1,\n  port: 1\n};\n\nexport const elm = {\n  name: \"elm\",\n  startState: function ()  { return { f: normal() }; },\n  copyState:  function (s) { return { f: s.f }; },\n\n  token: function(stream, state) {\n    var type = state.f(stream, function(s) { state.f = s; });\n    var word = stream.current();\n    return (wellKnownWords.hasOwnProperty(word)) ? 'keyword' : type;\n  },\n\n  languageData: {\n    commentTokens: {line: \"--\"}\n  }\n};\n"], "names": [], "sourceRoot": ""}