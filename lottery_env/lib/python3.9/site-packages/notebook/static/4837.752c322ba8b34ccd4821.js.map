{"version": 3, "file": "4837.752c322ba8b34ccd4821.js?v=752c322ba8b34ccd4821", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAsD;AACX;AAC3C;AACA;AACA;AACO,iCAAiC,sBAAQ;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,mBAAS;AAC/B,SAAS;AACT;AACA;AACA;;;;;ACjB0C;AAC1C;AACA;AACA;AACO,0BAA0B,oBAAK;;;ACJN;AACR", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree/lib/notebook-tree.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree/lib/token.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/tree/lib/index.js"], "sourcesContent": ["import { TabBarSvg } from '@jupyterlab/ui-components';\nimport { TabPanel } from '@lumino/widgets';\n/**\n * The widget added in main area of the tree view.\n */\nexport class NotebookTreeWidget extends TabPanel {\n    /**\n     * Constructor of the NotebookTreeWidget.\n     */\n    constructor() {\n        super({\n            tabPlacement: 'top',\n            tabsMovable: true,\n            renderer: TabBarSvg.defaultRenderer,\n        });\n        this.addClass('jp-TreePanel');\n    }\n}\n", "import { Token } from '@lumino/coreutils';\n/**\n * The INotebookTree token.\n */\nexport const INotebookTree = new Token('@jupyter-notebook/tree:INotebookTree');\n", "export * from './notebook-tree';\nexport * from './token';\n"], "names": [], "sourceRoot": ""}