{"version": 3, "file": "2386.4a6f7defebb9a3696820.js?v=4a6f7defebb9a3696820", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,sCAAsC,YAAY;AAClD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEO;AACP;;AAEA;AACA,YAAY;AACZ,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,2BAA2B;AAC3B,oBAAoB,QAAQ;AAC5B;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/pascal.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nvar keywords = words(\n  \"absolute and array asm begin case const constructor destructor div do \" +\n    \"downto else end file for function goto if implementation in inherited \" +\n    \"inline interface label mod nil not object of operator or packed procedure \" +\n    \"program record reintroduce repeat self set shl shr string then to type \" +\n    \"unit until uses var while with xor as class dispinterface except exports \" +\n    \"finalization finally initialization inline is library on out packed \" +\n    \"property raise resourcestring threadvar try absolute abstract alias \" +\n    \"assembler bitpacked break cdecl continue cppdecl cvar default deprecated \" +\n    \"dynamic enumerator experimental export external far far16 forward generic \" +\n    \"helper implements index interrupt iocheck local message name near \" +\n    \"nodefault noreturn nostackframe oldfpccall otherwise overload override \" +\n    \"pascal platform private protected public published read register \" +\n    \"reintroduce result safecall saveregisters softfloat specialize static \" +\n    \"stdcall stored strict unaligned unimplemented varargs virtual write\");\nvar atoms = {\"null\": true};\n\nvar isOperatorChar = /[+\\-*&%=<>!?|\\/]/;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == \"#\" && state.startOfLine) {\n    stream.skipToEnd();\n    return \"meta\";\n  }\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (ch == \"(\" && stream.eat(\"*\")) {\n    state.tokenize = tokenComment;\n    return tokenComment(stream, state);\n  }\n  if (ch == \"{\") {\n    state.tokenize = tokenCommentBraces;\n    return tokenCommentBraces(stream, state);\n  }\n  if (/[\\[\\]\\(\\),;\\:\\.]/.test(ch)) {\n    return null;\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current().toLowerCase();\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped) state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \")\" && maybeEnd) {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenCommentBraces(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"}\") {\n      state.tokenize = null;\n      break;\n    }\n  }\n  return \"comment\";\n}\n\n// Interface\n\nexport const pascal = {\n  name: \"pascal\",\n\n  startState: function() {\n    return {tokenize: null};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\" || style == \"meta\") return style;\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {block: {open: \"(*\", close: \"*)\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}