{"version": 3, "file": "3211.2e93fd406e5c4e53774f.js?v=2e93fd406e5c4e53774f", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,gBAAgB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,qBAAqB,EAAE;AACvB,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA,4BAA4B,EAAE;AAC9B,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA,yHAAyH,IAAI;AAC7H;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,iBAAiB;AACjB,KAAK;AACL;AACA,iBAAiB;AACjB;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,IAAI;AAChC;AACA,KAAK;AACL;AACA,oBAAoB;AACpB,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,oCAAoC,SAAS;AAC7C;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/livescript.js"], "sourcesContent": ["var tokenBase = function(stream, state) {\n  var next_rule = state.next || \"start\";\n  if (next_rule) {\n    state.next = state.next;\n    var nr = Rules[next_rule];\n    if (nr.splice) {\n      for (var i$ = 0; i$ < nr.length; ++i$) {\n        var r = nr[i$];\n        if (r.regex && stream.match(r.regex)) {\n          state.next = r.next || state.next;\n          return r.token;\n        }\n      }\n      stream.next();\n      return 'error';\n    }\n    if (stream.match(r = Rules[next_rule])) {\n      if (r.regex && stream.match(r.regex)) {\n        state.next = r.next;\n        return r.token;\n      } else {\n        stream.next();\n        return 'error';\n      }\n    }\n  }\n  stream.next();\n  return 'error';\n};\n\nvar identifier = '(?![\\\\d\\\\s])[$\\\\w\\\\xAA-\\\\uFFDC](?:(?!\\\\s)[$\\\\w\\\\xAA-\\\\uFFDC]|-[A-Za-z])*';\nvar indenter = RegExp('(?:[({[=:]|[-~]>|\\\\b(?:e(?:lse|xport)|d(?:o|efault)|t(?:ry|hen)|finally|import(?:\\\\s*all)?|const|var|let|new|catch(?:\\\\s*' + identifier + ')?))\\\\s*$');\nvar keywordend = '(?![$\\\\w]|-[A-Za-z]|\\\\s*:(?![:=]))';\nvar stringfill = {\n  token: 'string',\n  regex: '.+'\n};\nvar Rules = {\n  start: [\n    {\n      token: 'docComment',\n      regex: '/\\\\*',\n      next: 'comment'\n    }, {\n      token: 'comment',\n      regex: '#.*'\n    }, {\n      token: 'keyword',\n      regex: '(?:t(?:h(?:is|row|en)|ry|ypeof!?)|c(?:on(?:tinue|st)|a(?:se|tch)|lass)|i(?:n(?:stanceof)?|mp(?:ort(?:\\\\s+all)?|lements)|[fs])|d(?:e(?:fault|lete|bugger)|o)|f(?:or(?:\\\\s+own)?|inally|unction)|s(?:uper|witch)|e(?:lse|x(?:tends|port)|val)|a(?:nd|rguments)|n(?:ew|ot)|un(?:less|til)|w(?:hile|ith)|o[fr]|return|break|let|var|loop)' + keywordend\n    }, {\n      token: 'atom',\n      regex: '(?:true|false|yes|no|on|off|null|void|undefined)' + keywordend\n    }, {\n      token: 'invalid',\n      regex: '(?:p(?:ackage|r(?:ivate|otected)|ublic)|i(?:mplements|nterface)|enum|static|yield)' + keywordend\n    }, {\n      token: 'className.standard',\n      regex: '(?:R(?:e(?:gExp|ferenceError)|angeError)|S(?:tring|yntaxError)|E(?:rror|valError)|Array|Boolean|Date|Function|Number|Object|TypeError|URIError)' + keywordend\n    }, {\n      token: 'variableName.function.standard',\n      regex: '(?:is(?:NaN|Finite)|parse(?:Int|Float)|Math|JSON|(?:en|de)codeURI(?:Component)?)' + keywordend\n    }, {\n      token: 'variableName.standard',\n      regex: '(?:t(?:hat|il|o)|f(?:rom|allthrough)|it|by|e)' + keywordend\n    }, {\n      token: 'variableName',\n      regex: identifier + '\\\\s*:(?![:=])'\n    }, {\n      token: 'variableName',\n      regex: identifier\n    }, {\n      token: 'operatorKeyword',\n      regex: '(?:\\\\.{3}|\\\\s+\\\\?)'\n    }, {\n      token: 'keyword',\n      regex: '(?:@+|::|\\\\.\\\\.)',\n      next: 'key'\n    }, {\n      token: 'operatorKeyword',\n      regex: '\\\\.\\\\s*',\n      next: 'key'\n    }, {\n      token: 'string',\n      regex: '\\\\\\\\\\\\S[^\\\\s,;)}\\\\]]*'\n    }, {\n      token: 'docString',\n      regex: '\\'\\'\\'',\n      next: 'qdoc'\n    }, {\n      token: 'docString',\n      regex: '\"\"\"',\n      next: 'qqdoc'\n    }, {\n      token: 'string',\n      regex: '\\'',\n      next: 'qstring'\n    }, {\n      token: 'string',\n      regex: '\"',\n      next: 'qqstring'\n    }, {\n      token: 'string',\n      regex: '`',\n      next: 'js'\n    }, {\n      token: 'string',\n      regex: '<\\\\[',\n      next: 'words'\n    }, {\n      token: 'regexp',\n      regex: '//',\n      next: 'heregex'\n    }, {\n      token: 'regexp',\n      regex: '\\\\/(?:[^[\\\\/\\\\n\\\\\\\\]*(?:(?:\\\\\\\\.|\\\\[[^\\\\]\\\\n\\\\\\\\]*(?:\\\\\\\\.[^\\\\]\\\\n\\\\\\\\]*)*\\\\])[^[\\\\/\\\\n\\\\\\\\]*)*)\\\\/[gimy$]{0,4}',\n      next: 'key'\n    }, {\n      token: 'number',\n      regex: '(?:0x[\\\\da-fA-F][\\\\da-fA-F_]*|(?:[2-9]|[12]\\\\d|3[0-6])r[\\\\da-zA-Z][\\\\da-zA-Z_]*|(?:\\\\d[\\\\d_]*(?:\\\\.\\\\d[\\\\d_]*)?|\\\\.\\\\d[\\\\d_]*)(?:e[+-]?\\\\d[\\\\d_]*)?[\\\\w$]*)'\n    }, {\n      token: 'paren',\n      regex: '[({[]'\n    }, {\n      token: 'paren',\n      regex: '[)}\\\\]]',\n      next: 'key'\n    }, {\n      token: 'operatorKeyword',\n      regex: '\\\\S+'\n    }, {\n      token: 'content',\n      regex: '\\\\s+'\n    }\n  ],\n  heregex: [\n    {\n      token: 'regexp',\n      regex: '.*?//[gimy$?]{0,4}',\n      next: 'start'\n    }, {\n      token: 'regexp',\n      regex: '\\\\s*#{'\n    }, {\n      token: 'comment',\n      regex: '\\\\s+(?:#.*)?'\n    }, {\n      token: 'regexp',\n      regex: '\\\\S+'\n    }\n  ],\n  key: [\n    {\n      token: 'operatorKeyword',\n      regex: '[.?@!]+'\n    }, {\n      token: 'variableName',\n      regex: identifier,\n      next: 'start'\n    }, {\n      token: 'content',\n      regex: '',\n      next: 'start'\n    }\n  ],\n  comment: [\n    {\n      token: 'docComment',\n      regex: '.*?\\\\*/',\n      next: 'start'\n    }, {\n      token: 'docComment',\n      regex: '.+'\n    }\n  ],\n  qdoc: [\n    {\n      token: 'string',\n      regex: \".*?'''\",\n      next: 'key'\n    }, stringfill\n  ],\n  qqdoc: [\n    {\n      token: 'string',\n      regex: '.*?\"\"\"',\n      next: 'key'\n    }, stringfill\n  ],\n  qstring: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\\\']*(?:\\\\\\\\.[^\\\\\\\\\\']*)*\\'',\n      next: 'key'\n    }, stringfill\n  ],\n  qqstring: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\\"]*(?:\\\\\\\\.[^\\\\\\\\\"]*)*\"',\n      next: 'key'\n    }, stringfill\n  ],\n  js: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\`]*(?:\\\\\\\\.[^\\\\\\\\`]*)*`',\n      next: 'key'\n    }, stringfill\n  ],\n  words: [\n    {\n      token: 'string',\n      regex: '.*?\\\\]>',\n      next: 'key'\n    }, stringfill\n  ]\n};\nfor (var idx in Rules) {\n  var r = Rules[idx];\n  if (r.splice) {\n    for (var i = 0, len = r.length; i < len; ++i) {\n      var rr = r[i];\n      if (typeof rr.regex === 'string') {\n        Rules[idx][i].regex = new RegExp('^' + rr.regex);\n      }\n    }\n  } else if (typeof rr.regex === 'string') {\n    Rules[idx].regex = new RegExp('^' + r.regex);\n  }\n}\n\nexport const liveScript = {\n  name: \"livescript\",\n  startState: function(){\n    return {\n      next: 'start',\n      lastToken: {style: null, indent: 0, content: \"\"}\n    };\n  },\n  token: function(stream, state){\n    while (stream.pos == stream.start)\n      var style = tokenBase(stream, state);\n    state.lastToken = {\n      style: style,\n      indent: stream.indentation(),\n      content: stream.current()\n    };\n    return style.replace(/\\./g, ' ');\n  },\n  indent: function(state){\n    var indentation = state.lastToken.indent;\n    if (state.lastToken.content.match(indenter)) {\n      indentation += 2;\n    }\n    return indentation;\n  }\n};\n"], "names": [], "sourceRoot": ""}