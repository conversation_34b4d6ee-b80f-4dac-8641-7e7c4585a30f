#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球AI分析系统演示
展示多种分析策略和结果
"""

from lottery_analyzer import LotteryAnalyzer
import numpy as np
import time

def demo_multiple_predictions():
    """演示多种预测策略"""
    print("🎲 双色球AI分析系统 - 多策略演示")
    print("="*60)
    
    # 创建分析器
    analyzer = LotteryAnalyzer()
    analyzer.load_sample_data()
    
    # 获取基础统计数据
    red_df, blue_df = analyzer.basic_statistics()
    
    print("\n🔥 热门号码策略 (基于历史高频)")
    hot_strategy_red = red_df.head(6)['球号'].values.tolist()
    hot_strategy_blue = blue_df.iloc[0]['球号']
    print(f"红球: {' '.join([f'{int(x):02d}' for x in hot_strategy_red])}")
    print(f"蓝球: {int(hot_strategy_blue):02d}")
    
    print("\n❄️  冷门号码策略 (基于历史低频)")
    cold_strategy_red = red_df.tail(6)['球号'].values.tolist()
    cold_strategy_blue = blue_df.iloc[-1]['球号']
    print(f"红球: {' '.join([f'{int(x):02d}' for x in cold_strategy_red])}")
    print(f"蓝球: {int(cold_strategy_blue):02d}")
    
    print("\n⚖️  平衡策略 (热门+冷门组合)")
    balance_red = list(red_df.head(3)['球号'].values) + list(red_df.tail(3)['球号'].values)
    balance_red = sorted([int(x) for x in balance_red])
    balance_blue = blue_df.iloc[len(blue_df)//2]['球号']
    print(f"红球: {' '.join([f'{x:02d}' for x in balance_red])}")
    print(f"蓝球: {int(balance_blue):02d}")
    
    print("\n🎯 AI智能策略 (算法推演)")
    lucky_red, lucky_blue = analyzer.generate_lucky_numbers()
    
    print("\n📊 策略对比分析:")
    strategies = [
        ("热门策略", hot_strategy_red, hot_strategy_blue),
        ("冷门策略", cold_strategy_red, cold_strategy_blue),
        ("平衡策略", balance_red, balance_blue),
        ("AI策略", lucky_red, lucky_blue)
    ]
    
    for name, red_nums, blue_num in strategies:
        red_nums = [int(x) for x in red_nums]
        blue_num = int(blue_num)
        
        # 分析号码特征
        odd_count = sum(1 for x in red_nums if x % 2 == 1)
        even_count = 6 - odd_count
        small_count = sum(1 for x in red_nums if x <= 16)
        big_count = 6 - small_count
        span = max(red_nums) - min(red_nums)
        
        print(f"\n{name}:")
        print(f"  奇偶: {odd_count}奇{even_count}偶")
        print(f"  大小: {small_count}小{big_count}大")
        print(f"  跨度: {span}")
        print(f"  连号: {'有' if any(red_nums[i+1] - red_nums[i] == 1 for i in range(5)) else '无'}")

def demo_trend_analysis():
    """演示趋势分析"""
    print("\n\n📈 趋势分析演示")
    print("="*40)
    
    analyzer = LotteryAnalyzer()
    analyzer.load_sample_data()
    
    # 分析最近10期的趋势
    recent_data = analyzer.data.tail(10)
    
    print("2025年4月最近10期开奖号码:")
    for _, row in recent_data.iterrows():
        red_balls = [row[f'red{i}'] for i in range(1, 7)]
        blue_ball = row['blue']
        date = row['date'].strftime('%Y-%m-%d')
        print(f"{date}: {' '.join([f'{int(x):02d}' for x in red_balls])} + {int(blue_ball):02d}")
    
    # 分析趋势
    print(f"\n趋势分析:")
    
    # 最近出现的号码
    recent_reds = []
    for _, row in recent_data.iterrows():
        recent_reds.extend([row[f'red{i}'] for i in range(1, 7)])
    
    from collections import Counter
    red_counter = Counter(recent_reds)
    hot_recent = red_counter.most_common(5)
    
    print(f"2025年4月热门红球: {', '.join([f'{int(num):02d}({count}次)' for num, count in hot_recent])}")

    # 遗漏分析
    all_reds = set(range(1, 34))
    recent_reds_set = set([int(x) for x in recent_reds])
    missing_reds = all_reds - recent_reds_set

    print(f"2025年4月遗漏红球: {', '.join([f'{num:02d}' for num in sorted(missing_reds)[:10]])}")

def demo_probability_analysis():
    """演示概率分析"""
    print("\n\n🎲 概率分析演示")
    print("="*40)
    
    # 理论概率
    from math import factorial
    
    def combination(n, r):
        return factorial(n) // (factorial(r) * factorial(n - r))
    
    red_combinations = combination(33, 6)
    blue_combinations = 16
    total_combinations = red_combinations * blue_combinations
    
    print(f"双色球理论分析:")
    print(f"红球组合数: C(33,6) = {red_combinations:,}")
    print(f"蓝球组合数: {blue_combinations}")
    print(f"总组合数: {total_combinations:,}")
    print(f"一等奖概率: 1/{total_combinations:,} = {1/total_combinations:.10f}")
    
    # 不同奖项概率
    prizes = [
        ("一等奖", "6+1", 1),
        ("二等奖", "6+0", 15),
        ("三等奖", "5+1", combination(6,5) * combination(27,1) * 1),
        ("四等奖", "5+0或4+1", combination(6,5) * combination(27,1) * 15 + combination(6,4) * combination(27,2) * 1),
        ("五等奖", "4+0或3+1", combination(6,4) * combination(27,2) * 15 + combination(6,3) * combination(27,3) * 1),
        ("六等奖", "2+1或1+1或0+1", combination(6,2) * combination(27,4) * 1 + combination(6,1) * combination(27,5) * 1 + combination(6,0) * combination(27,6) * 1)
    ]
    
    print(f"\n各奖项中奖概率:")
    for prize_name, condition, combinations in prizes:
        probability = combinations / total_combinations
        print(f"{prize_name} ({condition}): 1/{total_combinations//combinations:,} = {probability:.8f}")

if __name__ == "__main__":
    print("🎯 双色球AI分析系统 - 完整演示")
    print("本演示将展示多种分析策略和概率计算")
    print("-" * 60)
    
    # 演示1: 多策略预测
    demo_multiple_predictions()
    
    # 演示2: 趋势分析
    demo_trend_analysis()
    
    # 演示3: 概率分析
    demo_probability_analysis()
    
    print(f"\n" + "="*60)
    print("🎊 演示完成！")
    print("💡 记住：彩票具有随机性，请理性购彩！")
    print("="*60)
